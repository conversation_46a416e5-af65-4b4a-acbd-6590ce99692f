﻿using Autodesk.Revit.DB.Electrical;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.CableManagement
{
    internal class PB_Resistance
    {
        private PB_Cable parent_cable;
        private PB_Conductivity parent_conductivity;
        private PB_CableProperties cable_properties;
        private string country;
        private string core_type;
        private string conductor_material;
        private bool isFlexible;
        private double size_mm2;
        private double temperature;
        private string resistance_id;
        private double value;

        public PB_Resistance(PB_Cable parent_cable, PB_Conductivity parent_conductivity, PB_CableProperties cable_properties)
        {
            this.parent_cable = parent_cable;
            this.parent_conductivity = parent_conductivity;
            this.cable_properties = cable_properties;

            this.country = parent_cable.country;
            this.core_type = parent_cable.coreType;
            this.conductor_material = parent_cable.conductorMaterial;
            this.isFlexible = parent_cable.isFlexible;
            this.size_mm2 = parent_conductivity.sizeMm2;
            this.temperature = parent_cable.ratedCurrent;

            if (size_mm2 < 0)
            {
                resistance_id = "-1";
                value = -1;
            }
            else
            {
                var result = GetPBResistanceId();
                this.resistance_id = result?.Item1 ?? "-1";
                this.value = result?.Item2 ?? -1;
            }
        }

        private Tuple<string, double> GetPBResistanceId()
        {
            DataTable resistance_df = cable_properties.resistanceDf;

            try
            {
                DataTable filtered_df = resistance_df.AsEnumerable()
                    .Where(row =>
                        row.Field<string>("Standard").Contains(country) &&
                        row.Field<string>("Core_Type").Contains(core_type) &&
                        row.Field<string>("Conductor_Material") == conductor_material &&
                        row.Field<bool>("isFlexible") == Convert.ToBoolean(isFlexible) &&
                        row.Field<double>("Size_mm2") == size_mm2 &&
                        row.Field<int>("Temperature") == temperature
                    )
                    .CopyToDataTable();

                if (filtered_df.Rows.Count > 0)
                {
                    var first_match = filtered_df.Rows[0];
                    return Tuple.Create(first_match.Field<string>("ID"), first_match.Field<double>("Resistance"));
                }
                else
                {
                    return null;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"An unexpected error occurred: {e}");
                return null;
            }
        }
    }
}
