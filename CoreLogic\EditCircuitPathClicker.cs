﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.UI;
using BecaRevitUtilities.RevitCommandsCaller;
using MEP.PowerBIM_5.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class EditCircuitPathClicker : RevitButtonClicker
    {

        #region Fields



        #endregion

        #region Properties



        #endregion

        #region Constructors

        private EditCircuitPathClicker(UIApplication uiApp) : base("Dialog_BuildingSystems_RbsEditCircuitGroup:Control_BuildingSystems_RbsEditCircuitPath", uiApp)
        {
           
        }

        public static EditCircuitPathClicker Create(UIApplication uiApp)
        {
            var clicker = new EditCircuitPathClicker(uiApp);
            if (clicker._isValid)
            {
                return clicker;
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region Methods
        public bool IsElementValidForSelection(Element e)
        {
            return e is ElectricalSystem;
        }

        protected override bool CanClick()
        {
            var selectedIds = _uiApp.ActiveUIDocument.Selection.GetElementIds();
            if (selectedIds.Count == 1)
            {
                var selElement = _uiApp.ActiveUIDocument.Document.GetElement(selectedIds.First());
                if (IsElementValidForSelection(selElement))
                {
                    return true;
                }
            }
            return false;
        }

        private void ComponentManager_ItemExecuted(object sender, Autodesk.Internal.Windows.RibbonItemExecutedEventArgs e)
        {
            if (e.Item.Id == "ID_RBS_CANCEL_CIRCUIT_PATH_EDIT_MODE")
            {
                //Common.UI.Forms.BecaBaseMessageForm.ShowDialog("Write code", "User canceled edit path command. please write code to handle user cancelation.");
                ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                ModelessPowerBIM_CircuitEditEnhancedFormHandler.BringFormsTofront();
                Autodesk.Windows.ComponentManager.ItemExecuted -= ComponentManager_ItemExecuted;
            }
            else if (e.Item.Id == "ID_RBS_FINISH_CIRCUIT_PATH_EDIT_MODE")
            {
                //Common.UI.Forms.BecaBaseMessageForm.ShowDialog("Write code", "User finsished edit path command. please write code to handle user edits.");
                ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                ModelessPowerBIM_CircuitEditEnhancedFormHandler.BringFormsTofront();
                Autodesk.Windows.ComponentManager.ItemExecuted -= ComponentManager_ItemExecuted;
                ModelessPowerBIM_CircuitEditEnhancedFormHandler.RecalcAndRefreshLengthToForm();

            }
        }

        protected override void PreClickEvent()
        {
            if (_isValid)
            {
                ModelessPowerBIM_CircuitEditEnhancedFormHandler.BringFormsToBack();
                Autodesk.Windows.ComponentManager.ItemExecuted += ComponentManager_ItemExecuted;

            }
        }

        #endregion

    }
}
