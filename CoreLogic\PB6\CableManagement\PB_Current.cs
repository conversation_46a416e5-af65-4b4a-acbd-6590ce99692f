﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.CableManagement
{
    internal class PB_Current
    {
        public PB_Cable parentCable { get; private set; }
        public PB_CableProperties cableProperties { get; private set; }
        public string country { get; private set; }
        public string coreType { get; private set; }
        public int phase { get; private set; }
        public string cableArrangement { get; private set; }
        public string conductorMaterial { get; private set; }
        public bool isFlexible { get; private set; }
        public string insulationMaterial { get; private set; }
        public double sizeMm2 { get; private set; }
        public Dictionary<string, PB_InstallationMethod> installationMethods { get; private set; }

        public PB_Current(PB_Cable parentCable, PB_CableProperties cableProperties)
        {
            this.parentCable = parentCable;
            this.cableProperties = cableProperties;

            country = parentCable.country;
            coreType = parentCable.coreType;
            phase = parentCable.phase;
            cableArrangement = parentCable.cableArrangement;
            conductorMaterial = parentCable.conductorMaterial;
            isFlexible = parentCable.isFlexible;
            insulationMaterial = parentCable.insulationMaterial;
            sizeMm2 = parentCable.activeSphMm2;
            installationMethods = new Dictionary<string, PB_InstallationMethod>();

            var result = GetPBCurrents();
            foreach (DataRow row in result.Rows)
            {
                string id = row[0].ToString();
                string cableArrangement = row[1].ToString();
                double ratedCurrent = Convert.ToDouble(row[2]);
                if (!installationMethods.ContainsKey(cableArrangement))
                {
                    installationMethods[cableArrangement] = new PB_InstallationMethod(cableArrangement, id, ratedCurrent);
                }
            }
        }

        private DataTable GetPBCurrents()
        {
            DataTable currentDf = cableProperties.currentDf;

            try
            {
                DataTable filteredDf = currentDf.AsEnumerable()
                    .Where(row => row.Field<string>("Standard").Contains(country) &&
                                   row.Field<string>("Core_Type").Contains(coreType.ToUpper()) &&
                                   row.Field<int>("Phase") == phase &&
                                   row.Field<string>("Conductor_Material") == conductorMaterial &&
                                   row.Field<bool>("IsFlexible") == isFlexible &&
                                   row.Field<double>("Size_mm2") == sizeMm2 &&
                                   row.Field<string>("Insulation_Material") == insulationMaterial)
                    .CopyToDataTable();

                if (filteredDf.Rows.Count > 0)
                {
                    DataTable result = new DataTable();
                    result.Columns.Add("ID", typeof(string));
                    result.Columns.Add("Cable_Arrangement", typeof(string));
                    result.Columns.Add("Rated_Current", typeof(double));

                    foreach (DataRow row in filteredDf.Rows)
                    {
                        result.Rows.Add(row["ID"], row["Cable_Arrangement"], row["Rated_Current"]);
                    }

                    return result;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"An unexpected error occurred: {e}");
                return null;
            }
        }
    }

    internal class PB_InstallationMethod
    {
        public string Method { get; set; }
        public string ID { get; set; }
        public double RatedCurrent { get; set; }

        public PB_InstallationMethod(string method, string id, double ratedCurrent)
        {
            Method = method;
            ID = id;
            RatedCurrent = ratedCurrent;
        }
    }
}
