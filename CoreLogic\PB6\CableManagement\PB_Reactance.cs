﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.CableManagement
{
    internal class PB_Reactance
    {
        public PB_Cable parentCable { get; private set; }
        public PB_Conductivity parentConductivity { get; private set; }
        public PB_CableProperties cableProperties { get; private set; }
        public string country { get; private set; }
        public string coreType { get; private set; }
        public string conductorArrangement { get; private set; }
        public bool isFlexible { get; private set; }
        public double sizeMm2 { get; private set; }
        public string insulationMaterial { get; private set; }
        public string reactanceId { get; private set; }
        public double value { get; private set; }

        public PB_Reactance(PB_Cable parentCable, PB_Conductivity parentConductivity, PB_CableProperties cableProperties)
        {
            this.parentCable = parentCable;
            this.parentConductivity = parentConductivity;
            this.cableProperties = cableProperties;

            country = parentCable.country;
            coreType = parentCable.coreType;

            conductorArrangement = parentCable.conductorArrangement;
            isFlexible = parentCable.isFlexible;
            sizeMm2 = parentConductivity.sizeMm2;
            insulationMaterial = parentCable.insulationMaterial;

            if (sizeMm2 < 0)
            {
                reactanceId = "-1";
                value = -1;
            }
            else
            {
                var result = GetPbReactanceId();
                reactanceId = result?.Item1 ?? "-1";
                value = result?.Item2 ?? -1;
            }

        }

        private Tuple<string, double> GetPbReactanceId()
        {
            DataTable reactanceDf = cableProperties.reactanceDf;
            
            try
            {
                DataTable filteredDf = reactanceDf.AsEnumerable().Where(row =>
                    row.Field<string>("Standard").Contains(country) &&
                    row.Field<string>("Core_Type").Contains(coreType) &&
                    row.Field<string>("Conductor_Arrangement") == conductorArrangement &&
                    row.Field<bool>("isFlexible") == Convert.ToBoolean(isFlexible) &&
                    row.Field<double>("Size_mm2") == sizeMm2 &&
                    row.Field<string>("Insulation_Material") == insulationMaterial
                ).CopyToDataTable();

                if (filteredDf.Rows.Count > 0)
                {
                    var firstMatch = filteredDf.Rows[0];
                    return Tuple.Create(firstMatch.Field<string>("ID"), firstMatch.Field<double>("Reactance"));
                }
                else
                {
                    return null;
                }
            }
            catch (KeyNotFoundException e)
            {
                Console.WriteLine($"Error: Missing expected column in DataFrame: {e}");
                return null;
            }
            catch (Exception e)
            {
                Console.WriteLine($"An unexpected error occurred: {e}");
                return null;
            }
        }
    }
}
