﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaRevitUtilities;
using BecaRevitUtilities.SharedParametersUtilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application = Autodesk.Revit.ApplicationServices.Application;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class PowerBIM_ParameterCheck
    {
        public bool RequiredSharedParameterFileExists { get; set; }

        private readonly List<ProjectParameterData> _projectParametersData;

        private readonly List<string> _projectInfoParameterNames = new List<string>()
        {
            "Beca_Electrical_Engineer",
            "Beca_Electrical_Verifier",
            "Beca_PB_VD_Calc_Other",
            "Beca_PB_VD_Calc_Power",
            "Beca_PB_VD_Calc_Lighting",
            "Beca_PB_GPO_Calc",
            "Beca_PB_Clearing_Time_Lighting",
            "Beca_PB_Clearing_Time_Power",
            "Beca_PB_Additional_Length_Per_Circuit_Lighting",
            "Beca_PB_Additional_Length_Per_Elem_Lighting",
            "Beca_PB_Additional_Length_Per_Circuit_Power",
            "Beca_PB_Additional_Length_Per_Elem_Power",
            "Beca_PB_Additional_Length_Per_Circuit",
            "Beca_PB_Additional_Length_Per_Elem",
            "Beca_PB_Revit_Path_Tolerance",
            "Beca_CPD_Manufacturer",
            "Beca System Max VDpc",
            "Beca_Discrimination_Test_Multiplier",
            "Beca_PB_Database_Path"
        };

        private readonly List<string> _circuitDataParameterNames = new List<string>()
        {
            "Beca_PB_Current_Manual",
            "Beca_PB_Current",
            "Beca Other ControlsInterface",
            "Beca RCD Protection",
            "Beca Circuit Cbl to First Circuit Component",
            "Beca Circuit Cable to Remainder of Circuit",
            "Beca Length to First Circuit Elem",
            "Beca Total Circuit Length",
            "Beca Circuit Installation Method",
            "Beca_Derating_Factor",
            "Beca_Diversity",
            "Beca Circuit Revision",
            "Beca Circuit Chk_Data",
            "Beca Circuit Chk_CBL1",
            "Beca Circuit Chk_CBLRM",
            "Beca Circuit Chk_Current1",
            "Beca Circuit Chk_Current2",
            "Beca Circuit Chk_Current3",
            "Beca Circuit Chk_Current4",
            "Beca Circuit Chk_EFLI",
            "Beca Circuit Chk_VD1",
            "Beca Circuit Chk_VD2",
            "Beca_Circuit_Chk_SC1",
            "Beca_Circuit_Chk_SC2",
            "Beca Circuit Chk_Result",
            "Beca Circuit Check Result"
        };

        private readonly List<string> _beakerDataParameterNames = new List<string>()
        {
            "Beca Protection Curve Type",
            "Beca Protection Device"
        };

        private readonly List<string> _dBDataParameterNames = new List<string>()
        {
            "Beca Feeder Cable Size",
            "Beca Location",
            "Beca Siesmic Category",
            "Beca_DB_Form_Rating",
            "Beca_DB_Bus_Fault_Level",
            "Beca_DB_Surge_Protection",
            "Beca_DB_Metering",
            "Beca DB Final Circuit Max VD",
            "Beca DB EFLiR",
            "Beca DB EFLiX",
            "Beca DB VD",
            "Beca DB PSCC",
            "Beca DB Upstream Device Rating",
            "Beca DB Chk",
            "Beca_Diversified_Amps_R",
            "Beca_Diversified_Amps_W",
            "Beca_Diversified_Amps_B"
        };

        private readonly List<string> _dBAndCircuitParameterNames = new List<string>()
        {
            "Beca_Circuit_Length_Manual",
            "Beca_PB_IsLocked"
        };

        public PowerBIM_ParameterCheck(Document doc)
        {
            RequiredSharedParameterFileExists = true;
            if (!ParametersIsAllGood(doc))
            {
                RequiredSharedParameterFileExists = CheckShareParameterFile(doc);
                if (!RequiredSharedParameterFileExists)
                {
                    return;
                }

                _projectParametersData = GetProjectParameterData(doc);

                // Check parameters in wrong categories (for old projects)
                //CheckParametersInWrongCategories(doc);
                // Check Project Info Parameters
#if  TargetYear2025
            CheckSingleCategoryParameters(doc, BuiltInCategory.OST_ProjectInformation, GroupTypeId.Electrical, _projectInfoParameterNames);
            // Check Circuit Parameters
            CheckSingleCategoryParameters(doc, BuiltInCategory.OST_ElectricalCircuit, GroupTypeId.ElectricalCircuiting, _circuitDataParameterNames);
            // Check Breaker Parameters
            CheckSingleCategoryParameters(doc, BuiltInCategory.OST_ElectricalCircuit, GroupTypeId.Electrical, _beakerDataParameterNames);
            // Check DB Parameters
            CheckSingleCategoryParameters(doc, BuiltInCategory.OST_ElectricalEquipment, GroupTypeId.Electrical, _dBDataParameterNames);

            // Check DB and Circuit Parameters
            CheckMultipleCategoryParameters(doc, new List<BuiltInCategory> { BuiltInCategory.OST_ElectricalEquipment, BuiltInCategory.OST_ElectricalCircuit },
                GroupTypeId.ElectricalCircuiting, _dBAndCircuitParameterNames);
            var d = GroupTypeId.Materials;
#else
                CheckSingleCategoryParameters(doc, BuiltInCategory.OST_ProjectInformation, BuiltInParameterGroup.PG_ELECTRICAL, _projectInfoParameterNames);
                // Check Circuit Parameters
                CheckSingleCategoryParameters(doc, BuiltInCategory.OST_ElectricalCircuit, BuiltInParameterGroup.PG_ELECTRICAL_CIRCUITING, _circuitDataParameterNames);
                // Check Breaker Parameters
                CheckSingleCategoryParameters(doc, BuiltInCategory.OST_ElectricalCircuit, BuiltInParameterGroup.PG_ELECTRICAL, _beakerDataParameterNames);
                // Check DB Parameters
                CheckSingleCategoryParameters(doc, BuiltInCategory.OST_ElectricalEquipment, BuiltInParameterGroup.PG_ELECTRICAL, _dBDataParameterNames);

                // Check DB and Circuit Parameters
                CheckMultipleCategoryParameters(doc, new List<BuiltInCategory> { BuiltInCategory.OST_ElectricalEquipment, BuiltInCategory.OST_ElectricalCircuit },
                    BuiltInParameterGroup.PG_ELECTRICAL_CIRCUITING, _dBAndCircuitParameterNames);
                //var d = ParameterGroup.PG_MATERIALS;
#endif
            }
        }

        public bool CheckShareParameterFile(Document doc)
        {
            doc.Application.SharedParametersFilename = PowerBIM_Constants.SharedParameterPath + "\\Beca_77_ELEC_Shared_Parameters.txt";
            DefinitionFile defFile = doc.Application.OpenSharedParameterFile();

            if (defFile == null)
            {
                MessageBox.Show($"Can't find Shared Parameter file in:\n\n{PowerBIM_Constants.SharedParameterPath}");
                return false;
            }
            else
            {
                return true;
            }
        }

        private bool ParametersIsAllGood(Document doc)
        {
            List<string> missingProjectInfoParameters = CheckProjectInfoParameters(doc, _projectInfoParameterNames);
            List<string> missingCircuitDataParameters = CheckCategoryParameters(doc, BuiltInCategory.OST_ElectricalCircuit, _circuitDataParameterNames);
            List<string> missingBreakerDataParameters = CheckCategoryParameters(doc, BuiltInCategory.OST_ElectricalCircuit, _beakerDataParameterNames);
            List<string> missingDBDataParameters = CheckCategoryParameters(doc, BuiltInCategory.OST_ElectricalEquipment, _dBDataParameterNames);

            List<string> missingDBAndCircuitParameters = CheckCategoryParameters(doc, BuiltInCategory.OST_ElectricalCircuit, _dBAndCircuitParameterNames)
                .Concat(CheckCategoryParameters(doc, BuiltInCategory.OST_ElectricalEquipment, _dBAndCircuitParameterNames)).Distinct().ToList();

            bool anyMissing = missingProjectInfoParameters.Any() || missingCircuitDataParameters.Any() || missingBreakerDataParameters.Any() || missingDBDataParameters.Any() || missingDBAndCircuitParameters.Any();

#if TargetYear2020 || TargetYear2021 || TargetYear2022
            BuiltInParameterGroup electricalGroup = BuiltInParameterGroup.PG_ELECTRICAL;
#else
            ForgeTypeId electricalGroup = GroupTypeId.Electrical;
#endif

            if (anyMissing)
            {
                TaskDialog mainDialog = new TaskDialog("Missing Parameters")
                {
                    MainInstruction = "The following parameters are missing:",
                    MainContent =
                        "Project Info Parameters:\n" + string.Join("\n", missingProjectInfoParameters) + "\n\n" +
                        "Circuit Data Parameters:\n" + string.Join("\n", missingCircuitDataParameters) + "\n\n" +
                        "Breaker Data Parameters:\n" + string.Join("\n", missingBreakerDataParameters) + "\n\n" +
                        "DB Data Parameters:\n" + string.Join("\n", missingDBDataParameters) + "\n\n" +
                        "DB and Ciruit Parameters:\n" + string.Join("\n", missingDBAndCircuitParameters) + "\n\n" +
                        "Do you want to create these parameters?",
                    CommonButtons = TaskDialogCommonButtons.Yes | TaskDialogCommonButtons.No,
                    DefaultButton = TaskDialogResult.Yes
                };

                TaskDialogResult result = mainDialog.Show();

                if (result == TaskDialogResult.Yes)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else
            {
                return true;
            }
        }

        private List<string> CheckCategoryParameters(Document doc, BuiltInCategory category, List<string> parameterNames)
        {
            List<string> missingParameters = new List<string>();

            Category cat = doc.Settings.Categories.get_Item(category);
            if (cat != null)
            {
                foreach (string paramName in parameterNames)
                {
                    bool paramExists = false;
                    foreach (Element element in new FilteredElementCollector(doc).OfCategoryId(cat.Id).WhereElementIsNotElementType())
                    {
                        Parameter param = element.LookupParameter(paramName);
                        if (param != null)
                        {
                            paramExists = true;
                            break;
                        }
                    }
                    if (!paramExists)
                    {
                        missingParameters.Add(paramName);
                    }
                }
            }

            return missingParameters;
        }

        private List<string> CheckProjectInfoParameters(Document doc, List<string> parameterNames)
        {
            List<string> missingParameters = new List<string>();

            ProjectInfo projectInfo = doc.ProjectInformation;
            if (projectInfo != null)
            {
                foreach (string paramName in parameterNames)
                {
                    Parameter param = projectInfo.LookupParameter(paramName);
                    if (param == null)
                    {
                        missingParameters.Add(paramName);
                    }
                }
            }

            return missingParameters;
        }

        private void CheckParametersInWrongCategories(Document doc)
        {
            var projectInfo = doc.ProjectInformation;
            var circuit = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_ElectricalCircuit).WhereElementIsNotElementType().FirstOrDefault();
            var db = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_ElectricalEquipment).WhereElementIsNotElementType().FirstOrDefault();

            var count = 0;
            var sb = new StringBuilder();
            IEnumerable<string> dBAndCircuitParameterNames = _dBAndCircuitParameterNames;
            // Check project info parameters
            CheckParametersInCategory(projectInfo, _circuitDataParameterNames.Concat(_beakerDataParameterNames), sb, ref count);
            CheckParametersInCategory(projectInfo, dBAndCircuitParameterNames, sb, ref count);

            // Check circuit parameters
            CheckParametersInCategory(circuit, _projectInfoParameterNames.Concat(_dBDataParameterNames), sb, ref count);

            // Check db parameters
            CheckParametersInCategory(db, _projectInfoParameterNames, sb, ref count);
            CheckParametersInCategory(db, _circuitDataParameterNames.Concat(_beakerDataParameterNames), sb, ref count);

            if (count > 0)
            {
                TaskDialog.Show("Parameter Check", $"{count} parameters are in wrong category.\nThese parameters have to be deleted:\n\n {sb}");
            }


        }

        private void CheckParametersInCategory(Element element, IEnumerable<string> parameterNames, StringBuilder sb, ref int count)
        {
            if (element == null)
            {
                return;
            }

            foreach (var parameterName in parameterNames)
            {
                var p = element.LookupParameter(parameterName);
                if (p != null)
                {
                    sb.AppendLine($"{element.Category.Name}: {p.Definition.Name}");
                    count++;
                }
            }
        }
#if  TargetYear2025
        private void CheckSingleCategoryParameters(Document doc, BuiltInCategory bic, ForgeTypeId groupTypeId, List<string> parameterNames)
        {
            CategorySet categorySet = new CategorySet();
            categorySet.Insert(Category.GetCategory(doc, bic));

            var parametersInCategory = _projectParametersData
                .Where(p => p.Binding.Categories.Contains(Category.GetCategory(doc, bic)))
                .Select(pd => pd.Name)
                .ToList();

            CheckParameters(doc, parametersInCategory, parameterNames, categorySet, groupTypeId);
        }
#else
        private void CheckSingleCategoryParameters(Document doc, BuiltInCategory bic, BuiltInParameterGroup group, List<string> parameterNames)
        {
            CategorySet categorySet = new CategorySet();
            categorySet.Insert(Category.GetCategory(doc, bic));

            var parametersInCategory = _projectParametersData.Where(p => p.Binding.Categories.Contains(Category.GetCategory(doc, bic))).Select(pd => pd.Name).ToList();

            CheckParameters(doc, parametersInCategory, parameterNames, categorySet, group);
        }
#endif

#if   TargetYear2025
        private void CheckMultipleCategoryParameters(Document doc, List<BuiltInCategory> bics, ForgeTypeId group, List<string> parameterNames)
        {
            CategorySet categorySet = new CategorySet();
            List<string> parametersInCategory = new List<string>();

            foreach (var bic in bics)
            {
                categorySet.Insert(Category.GetCategory(doc, bic));
                _projectParametersData.Where(p => p.Binding.Categories.Contains(Category.GetCategory(doc, bic))).Select(pd => pd.Name).ToList().ForEach(n => parametersInCategory.Add(n));
            }

            CheckParameters(doc, parametersInCategory, parameterNames, categorySet, group);
        }
#else
        private void CheckMultipleCategoryParameters(Document doc, List<BuiltInCategory> bics, BuiltInParameterGroup group, List<string> parameterNames)
        {
            CategorySet categorySet = new CategorySet();
            List<string> parametersInCategory = new List<string>();

            foreach (var bic in bics)
            {
                categorySet.Insert(Category.GetCategory(doc, bic));
                _projectParametersData.Where(p => p.Binding.Categories.Contains(Category.GetCategory(doc, bic))).Select(pd => pd.Name).ToList().ForEach(n => parametersInCategory.Add(n));
            }

            CheckParameters(doc, parametersInCategory, parameterNames, categorySet, group);
        }

#endif
#if  TargetYear2025
        private void CheckParameters(Document doc, List<string> parametersInCategory, List<string> parameterNames, CategorySet categorySet, ForgeTypeId groupTypeId)
        {
            foreach (var parameterName in parameterNames)
            {
                if (!parametersInCategory.Contains(parameterName))
                {
                    try
                        {
                            using (var trans = new Transaction(doc, "Add parameter"))
                            {
                                trans.Start();
                                SharedParameterUtility.AddProjectParameter(doc, doc.Application, parameterName, categorySet,
                                    groupTypeId, true, PowerBIM_Constants.SharedParameterPath + "\\Beca_77_ELEC_Shared_Parameters.txt");
                                trans.Commit();
                            }
                        }
                        catch (Exception e)
                        {
                            TaskDialog.Show("Parameter creation exception", e.Message);
                        }
                }
            }
        }
#else
        private void CheckParameters(Document doc, List<string> parametersInCategory, List<string> parameterNames, CategorySet categorySet, BuiltInParameterGroup group)
        {
            using (var trans = new Transaction(doc, "Add parameter"))
            {
                trans.Start();
                foreach (var parameterName in parameterNames)
                {
                    if (!parametersInCategory.Contains(parameterName))
                    {
                        SharedParameterUtility.AddProjectParameter(doc, doc.Application, parameterName, categorySet,
                                group, true, PowerBIM_Constants.SharedParameterPath + "\\Beca_77_ELEC_Shared_Parameters.txt");
                    }
                }
                trans.Commit();
            }
        }
#endif


        /// <summary>
        /// Returns a list of the objects containing 
        /// references to the project parameter definitions
        /// </summary>
        /// <param name="doc">The project document being quereied</param>
        /// <returns></returns>
        private List<ProjectParameterData> GetProjectParameterData(Document doc)
        {
            List<ProjectParameterData> result = new List<ProjectParameterData>();

            BindingMap map = doc.ParameterBindings;
            DefinitionBindingMapIterator it = map.ForwardIterator();
            it.Reset();
            while (it.MoveNext())
            {
                ProjectParameterData newProjectParameterData
                  = new ProjectParameterData();

                newProjectParameterData.Definition = it.Key;
                newProjectParameterData.Name = it.Key.Name;
                newProjectParameterData.Binding = it.Current
                  as ElementBinding;

                result.Add(newProjectParameterData);
            }
            return result;
        }

    }

    #region Data holding class
    #endregion
}
