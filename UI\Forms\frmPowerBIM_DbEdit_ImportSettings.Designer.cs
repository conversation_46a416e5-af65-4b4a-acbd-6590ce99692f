﻿
namespace MEP.PowerBIM_5.UI.Forms
{
    partial class frmPowerBIM_DbEdit_ImportSettings
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.rb_RemoveSuffixMatch = new System.Windows.Forms.RadioButton();
            this.tbAdd = new System.Windows.Forms.TextBox();
            this.tb_remove = new System.Windows.Forms.TextBox();
            this.rb_AddSuffix = new System.Windows.Forms.RadioButton();
            this.rb_ExactMatch = new System.Windows.Forms.RadioButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.rb_PLM = new System.Windows.Forms.RadioButton();
            this.tbDelimiter = new System.Windows.Forms.TextBox();
            this.rb_12 = new System.Windows.Forms.RadioButton();
            this.rb_AB = new System.Windows.Forms.RadioButton();
            this.rb_PL = new System.Windows.Forms.RadioButton();
            this.rb_NoneDuplicate = new System.Windows.Forms.RadioButton();
            this.btnImport = new System.Windows.Forms.Button();
            this.btnClose = new System.Windows.Forms.Button();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.btnSelectFile = new System.Windows.Forms.Button();
            this.rqTxtCSVsel = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.tableLayoutPanel3 = new System.Windows.Forms.TableLayoutPanel();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label7 = new System.Windows.Forms.Label();
            this.dgbDBMatchList = new System.Windows.Forms.DataGridView();
            this.lb_dbCSV = new System.Windows.Forms.ListBox();
            this.tableLayoutPanel4 = new System.Windows.Forms.TableLayoutPanel();
            this.tableLayoutPanel5 = new System.Windows.Forms.TableLayoutPanel();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.tableLayoutPanel3.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgbDBMatchList)).BeginInit();
            this.tableLayoutPanel4.SuspendLayout();
            this.tableLayoutPanel5.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.rb_RemoveSuffixMatch);
            this.groupBox1.Controls.Add(this.tbAdd);
            this.groupBox1.Controls.Add(this.tb_remove);
            this.groupBox1.Controls.Add(this.rb_AddSuffix);
            this.groupBox1.Controls.Add(this.rb_ExactMatch);
            this.groupBox1.Location = new System.Drawing.Point(3, 2);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Size = new System.Drawing.Size(468, 245);
            this.groupBox1.TabIndex = 3;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "PowerCAD CSV Import Settings";
            // 
            // label2
            // 
            this.label2.Font = new System.Drawing.Font("Microsoft Sans Serif", 7.8F, System.Drawing.FontStyle.Italic, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.label2.Location = new System.Drawing.Point(67, 171);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(377, 55);
            this.label2.TabIndex = 3;
            this.label2.Text = "Enter a delimiter (typically \"/\" or \"-\") for which PowerBIM will ignore any chara" +
    "tchers after in the CSV file, before trying to match";
            // 
            // label1
            // 
            this.label1.Font = new System.Drawing.Font("Microsoft Sans Serif", 7.8F, System.Drawing.FontStyle.Italic, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.label1.Location = new System.Drawing.Point(69, 89);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(364, 46);
            this.label1.TabIndex = 3;
            this.label1.Text = "Enter a suffix below to be added to the CSV entry before attempting to match with" +
    " the DB name in revit";
            // 
            // rb_RemoveSuffixMatch
            // 
            this.rb_RemoveSuffixMatch.AutoSize = true;
            this.rb_RemoveSuffixMatch.Location = new System.Drawing.Point(29, 137);
            this.rb_RemoveSuffixMatch.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_RemoveSuffixMatch.Name = "rb_RemoveSuffixMatch";
            this.rb_RemoveSuffixMatch.Size = new System.Drawing.Size(200, 20);
            this.rb_RemoveSuffixMatch.TabIndex = 2;
            this.rb_RemoveSuffixMatch.TabStop = true;
            this.rb_RemoveSuffixMatch.Text = "Remove Suffix After Delimiter";
            this.rb_RemoveSuffixMatch.UseVisualStyleBackColor = true;
            this.rb_RemoveSuffixMatch.CheckedChanged += new System.EventHandler(this.rb_removeSuffixMatch_CheckedChanged);
            // 
            // tbAdd
            // 
            this.tbAdd.Location = new System.Drawing.Point(260, 62);
            this.tbAdd.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tbAdd.Name = "tbAdd";
            this.tbAdd.Size = new System.Drawing.Size(95, 22);
            this.tbAdd.TabIndex = 4;
            this.tbAdd.Text = "A";
            this.tbAdd.TextChanged += new System.EventHandler(this.tbAdd_TextChanged);
            // 
            // tb_remove
            // 
            this.tb_remove.Location = new System.Drawing.Point(260, 137);
            this.tb_remove.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_remove.Name = "tb_remove";
            this.tb_remove.Size = new System.Drawing.Size(95, 22);
            this.tb_remove.TabIndex = 4;
            this.tb_remove.Text = "/";
            this.tb_remove.TextChanged += new System.EventHandler(this.tb_remove_TextChanged);
            this.tb_remove.Leave += new System.EventHandler(this.tb_remove_Leave);
            // 
            // rb_AddSuffix
            // 
            this.rb_AddSuffix.AutoSize = true;
            this.rb_AddSuffix.Location = new System.Drawing.Point(29, 63);
            this.rb_AddSuffix.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_AddSuffix.Name = "rb_AddSuffix";
            this.rb_AddSuffix.Size = new System.Drawing.Size(87, 20);
            this.rb_AddSuffix.TabIndex = 1;
            this.rb_AddSuffix.TabStop = true;
            this.rb_AddSuffix.Text = "Add Suffix";
            this.rb_AddSuffix.UseVisualStyleBackColor = true;
            this.rb_AddSuffix.CheckedChanged += new System.EventHandler(this.rb_AddSuffix_CheckedChanged);
            // 
            // rb_ExactMatch
            // 
            this.rb_ExactMatch.AutoSize = true;
            this.rb_ExactMatch.Location = new System.Drawing.Point(29, 30);
            this.rb_ExactMatch.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_ExactMatch.Name = "rb_ExactMatch";
            this.rb_ExactMatch.Size = new System.Drawing.Size(131, 20);
            this.rb_ExactMatch.TabIndex = 0;
            this.rb_ExactMatch.TabStop = true;
            this.rb_ExactMatch.Text = "None (1:1 Match) ";
            this.rb_ExactMatch.UseVisualStyleBackColor = true;
            this.rb_ExactMatch.CheckedChanged += new System.EventHandler(this.rb_ExactMatch_CheckedChanged);
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.rb_PLM);
            this.groupBox2.Controls.Add(this.tbDelimiter);
            this.groupBox2.Controls.Add(this.rb_12);
            this.groupBox2.Controls.Add(this.rb_AB);
            this.groupBox2.Controls.Add(this.rb_PL);
            this.groupBox2.Controls.Add(this.rb_NoneDuplicate);
            this.groupBox2.Location = new System.Drawing.Point(3, 251);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox2.Size = new System.Drawing.Size(468, 245);
            this.groupBox2.TabIndex = 3;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "Duplicate DB Data On Import";
            // 
            // label5
            // 
            this.label5.Font = new System.Drawing.Font("Microsoft Sans Serif", 7.8F, System.Drawing.FontStyle.Italic, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.label5.Location = new System.Drawing.Point(33, 26);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(413, 52);
            this.label5.TabIndex = 3;
            this.label5.Text = "Use this utility to cater for projects where there are multiple DBs in revit that" +
    " need to correspond to a single DB in the powerCAD model. i.e. split power and l" +
    "ighting chassis.";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(257, 89);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(175, 16);
            this.label3.TabIndex = 5;
            this.label3.Text = "Delimiter (Typically \"-\" or \"/\")";
            // 
            // rb_PLM
            // 
            this.rb_PLM.AutoSize = true;
            this.rb_PLM.Location = new System.Drawing.Point(36, 138);
            this.rb_PLM.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_PLM.Name = "rb_PLM";
            this.rb_PLM.Size = new System.Drawing.Size(81, 20);
            this.rb_PLM.TabIndex = 2;
            this.rb_PLM.TabStop = true;
            this.rb_PLM.Text = "P + L + M";
            this.rb_PLM.UseVisualStyleBackColor = true;
            this.rb_PLM.CheckedChanged += new System.EventHandler(this.rb_PLM_CheckedChanged);
            // 
            // tbDelimiter
            // 
            this.tbDelimiter.Location = new System.Drawing.Point(260, 113);
            this.tbDelimiter.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tbDelimiter.Name = "tbDelimiter";
            this.tbDelimiter.Size = new System.Drawing.Size(95, 22);
            this.tbDelimiter.TabIndex = 4;
            this.tbDelimiter.Text = "-";
            this.tbDelimiter.TextChanged += new System.EventHandler(this.tbDelimiter_TextChanged);
            this.tbDelimiter.Leave += new System.EventHandler(this.tbDelimiter_Leave);
            // 
            // rb_12
            // 
            this.rb_12.AutoSize = true;
            this.rb_12.Location = new System.Drawing.Point(36, 193);
            this.rb_12.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_12.Name = "rb_12";
            this.rb_12.Size = new System.Drawing.Size(55, 20);
            this.rb_12.TabIndex = 1;
            this.rb_12.TabStop = true;
            this.rb_12.Text = "1 + 2";
            this.rb_12.UseVisualStyleBackColor = true;
            this.rb_12.CheckedChanged += new System.EventHandler(this.rb_12_CheckedChanged);
            // 
            // rb_AB
            // 
            this.rb_AB.AutoSize = true;
            this.rb_AB.Location = new System.Drawing.Point(36, 166);
            this.rb_AB.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_AB.Name = "rb_AB";
            this.rb_AB.Size = new System.Drawing.Size(59, 20);
            this.rb_AB.TabIndex = 1;
            this.rb_AB.TabStop = true;
            this.rb_AB.Text = "A + B";
            this.rb_AB.UseVisualStyleBackColor = true;
            this.rb_AB.CheckedChanged += new System.EventHandler(this.rb_AB_CheckedChanged);
            // 
            // rb_PL
            // 
            this.rb_PL.AutoSize = true;
            this.rb_PL.Location = new System.Drawing.Point(36, 113);
            this.rb_PL.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_PL.Name = "rb_PL";
            this.rb_PL.Size = new System.Drawing.Size(57, 20);
            this.rb_PL.TabIndex = 1;
            this.rb_PL.TabStop = true;
            this.rb_PL.Text = "P + L";
            this.rb_PL.UseVisualStyleBackColor = true;
            this.rb_PL.CheckedChanged += new System.EventHandler(this.rb_PL_CheckedChanged);
            // 
            // rb_NoneDuplicate
            // 
            this.rb_NoneDuplicate.AutoSize = true;
            this.rb_NoneDuplicate.Location = new System.Drawing.Point(36, 86);
            this.rb_NoneDuplicate.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rb_NoneDuplicate.Name = "rb_NoneDuplicate";
            this.rb_NoneDuplicate.Size = new System.Drawing.Size(61, 20);
            this.rb_NoneDuplicate.TabIndex = 0;
            this.rb_NoneDuplicate.TabStop = true;
            this.rb_NoneDuplicate.Text = "None";
            this.rb_NoneDuplicate.UseVisualStyleBackColor = true;
            this.rb_NoneDuplicate.CheckedChanged += new System.EventHandler(this.rb_NoneDuplicate_CheckedChanged);
            // 
            // btnImport
            // 
            this.btnImport.Anchor = System.Windows.Forms.AnchorStyles.Left;
            this.btnImport.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnImport.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.25F, System.Drawing.FontStyle.Bold);
            this.btnImport.Location = new System.Drawing.Point(565, 4);
            this.btnImport.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(193, 50);
            this.btnImport.TabIndex = 4;
            this.btnImport.Text = "Import";
            this.btnImport.UseVisualStyleBackColor = false;
            this.btnImport.Click += new System.EventHandler(this.btnImport_Click);
            // 
            // btnClose
            // 
            this.btnClose.Anchor = System.Windows.Forms.AnchorStyles.Right;
            this.btnClose.AutoSize = true;
            this.btnClose.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.btnClose.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnClose.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.25F, System.Drawing.FontStyle.Bold);
            this.btnClose.ForeColor = System.Drawing.Color.White;
            this.btnClose.Location = new System.Drawing.Point(370, 6);
            this.btnClose.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(189, 47);
            this.btnClose.TabIndex = 4;
            this.btnClose.Text = "Close";
            this.btnClose.UseVisualStyleBackColor = false;
            this.btnClose.Click += new System.EventHandler(this.btnClose_Click_1);
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel2, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.label4, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel3, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel5, 0, 3);
            this.tableLayoutPanel1.Location = new System.Drawing.Point(4, 52);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 4;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 35F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 65F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 506F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 62F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(1131, 649);
            this.tableLayoutPanel1.TabIndex = 6;
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel2.ColumnCount = 2;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 18.00819F));
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 81.99181F));
            this.tableLayoutPanel2.Controls.Add(this.btnSelectFile, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.rqTxtCSVsel, 1, 0);
            this.tableLayoutPanel2.Location = new System.Drawing.Point(3, 30);
            this.tableLayoutPanel2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 1;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(1125, 48);
            this.tableLayoutPanel2.TabIndex = 4;
            // 
            // btnSelectFile
            // 
            this.btnSelectFile.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSelectFile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.btnSelectFile.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSelectFile.ForeColor = System.Drawing.Color.White;
            this.btnSelectFile.Location = new System.Drawing.Point(3, 2);
            this.btnSelectFile.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnSelectFile.Name = "btnSelectFile";
            this.btnSelectFile.Size = new System.Drawing.Size(196, 44);
            this.btnSelectFile.TabIndex = 4;
            this.btnSelectFile.Text = "Select File";
            this.btnSelectFile.UseVisualStyleBackColor = false;
            this.btnSelectFile.Click += new System.EventHandler(this.btnSelectFile_Click);
            // 
            // rqTxtCSVsel
            // 
            this.rqTxtCSVsel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.rqTxtCSVsel.Location = new System.Drawing.Point(205, 13);
            this.rqTxtCSVsel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rqTxtCSVsel.Name = "rqTxtCSVsel";
            this.rqTxtCSVsel.Size = new System.Drawing.Size(917, 22);
            this.rqTxtCSVsel.TabIndex = 5;
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.Font = new System.Drawing.Font("Microsoft Sans Serif", 10.2F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(3, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(1125, 28);
            this.label4.TabIndex = 6;
            this.label4.Text = "Select the PowerCAD \'Network Summary\' CSV file. Then use the setting below to cor" +
    "rectly map the DBs";
            this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tableLayoutPanel3
            // 
            this.tableLayoutPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel3.ColumnCount = 2;
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 480F));
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel3.Controls.Add(this.groupBox3, 1, 0);
            this.tableLayoutPanel3.Controls.Add(this.tableLayoutPanel4, 0, 0);
            this.tableLayoutPanel3.Location = new System.Drawing.Point(3, 82);
            this.tableLayoutPanel3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel3.Name = "tableLayoutPanel3";
            this.tableLayoutPanel3.RowCount = 1;
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel3.Size = new System.Drawing.Size(1125, 502);
            this.tableLayoutPanel3.TabIndex = 4;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.dgbDBMatchList);
            this.groupBox3.Controls.Add(this.lb_dbCSV);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox3.Location = new System.Drawing.Point(484, 4);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.groupBox3.Size = new System.Drawing.Size(637, 494);
            this.groupBox3.TabIndex = 12;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "Match List";
            // 
            // label7
            // 
            this.label7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label7.Location = new System.Drawing.Point(447, 17);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(108, 17);
            this.label7.TabIndex = 11;
            this.label7.Text = "PowerCAD List";
            // 
            // dgbDBMatchList
            // 
            this.dgbDBMatchList.AllowUserToAddRows = false;
            this.dgbDBMatchList.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgbDBMatchList.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgbDBMatchList.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgbDBMatchList.Location = new System.Drawing.Point(7, 22);
            this.dgbDBMatchList.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dgbDBMatchList.Name = "dgbDBMatchList";
            this.dgbDBMatchList.RowHeadersWidth = 51;
            this.dgbDBMatchList.RowTemplate.Height = 24;
            this.dgbDBMatchList.Size = new System.Drawing.Size(433, 455);
            this.dgbDBMatchList.TabIndex = 1;
            // 
            // lb_dbCSV
            // 
            this.lb_dbCSV.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lb_dbCSV.FormattingEnabled = true;
            this.lb_dbCSV.ItemHeight = 16;
            this.lb_dbCSV.Location = new System.Drawing.Point(451, 41);
            this.lb_dbCSV.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.lb_dbCSV.Name = "lb_dbCSV";
            this.lb_dbCSV.Size = new System.Drawing.Size(177, 436);
            this.lb_dbCSV.TabIndex = 8;
            this.lb_dbCSV.DoubleClick += new System.EventHandler(this.lb_dbCSV_DoubleClick);
            // 
            // tableLayoutPanel4
            // 
            this.tableLayoutPanel4.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel4.ColumnCount = 1;
            this.tableLayoutPanel4.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel4.Controls.Add(this.groupBox2, 0, 1);
            this.tableLayoutPanel4.Controls.Add(this.groupBox1, 0, 0);
            this.tableLayoutPanel4.Location = new System.Drawing.Point(3, 2);
            this.tableLayoutPanel4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel4.Name = "tableLayoutPanel4";
            this.tableLayoutPanel4.RowCount = 2;
            this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel4.Size = new System.Drawing.Size(474, 498);
            this.tableLayoutPanel4.TabIndex = 0;
            // 
            // tableLayoutPanel5
            // 
            this.tableLayoutPanel5.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel5.ColumnCount = 2;
            this.tableLayoutPanel5.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel5.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel5.Controls.Add(this.btnClose, 0, 0);
            this.tableLayoutPanel5.Controls.Add(this.btnImport, 1, 0);
            this.tableLayoutPanel5.Location = new System.Drawing.Point(3, 588);
            this.tableLayoutPanel5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel5.Name = "tableLayoutPanel5";
            this.tableLayoutPanel5.RowCount = 1;
            this.tableLayoutPanel5.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel5.Size = new System.Drawing.Size(1125, 59);
            this.tableLayoutPanel5.TabIndex = 7;
            // 
            // frmPowerBIM_DbEdit_ImportSettings
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.btnHelp_Visiblity = true;
            this.ClientSize = new System.Drawing.Size(1139, 767);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.Name = "frmPowerBIM_DbEdit_ImportSettings";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "PowerBM | Import Settings";
            this.TitleText = "DB Import Settings";
            this.VerisionText = "© 2021   01.05.01  ";
            this.Controls.SetChildIndex(this.tableLayoutPanel1, 0);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            this.tableLayoutPanel2.PerformLayout();
            this.tableLayoutPanel3.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgbDBMatchList)).EndInit();
            this.tableLayoutPanel4.ResumeLayout(false);
            this.tableLayoutPanel5.ResumeLayout(false);
            this.tableLayoutPanel5.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RadioButton rb_RemoveSuffixMatch;
        private System.Windows.Forms.RadioButton rb_AddSuffix;
        private System.Windows.Forms.RadioButton rb_ExactMatch;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.RadioButton rb_PLM;
        private System.Windows.Forms.TextBox tbDelimiter;
        private System.Windows.Forms.RadioButton rb_PL;
        private System.Windows.Forms.RadioButton rb_NoneDuplicate;
        private System.Windows.Forms.RadioButton rb_12;
        private System.Windows.Forms.RadioButton rb_AB;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.Button btnClose;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.Button btnSelectFile;
        private System.Windows.Forms.TextBox rqTxtCSVsel;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel3;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel4;
        private System.Windows.Forms.DataGridView dgbDBMatchList;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel5;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox tbAdd;
        private System.Windows.Forms.TextBox tb_remove;
        private System.Windows.Forms.ListBox lb_dbCSV;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label7;
    }
}