﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB.Electrical;
using BecaTransactionsNamesManager;
using BecaRevitUtilities;
using BecaRevitUtilities.Collectors;
using BecaRevitCommonAlgorithms;
using GEN.Easy3DView.CoreLogic;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitElementsCreators;
using Autodesk.Revit.UI.Events;
using BecaActivityLogger.UI.Forms;
using View = Autodesk.Revit.DB.View;
using BecaRevitUtilities.RevitCommandsCaller;
using System.Windows.Forms;
using BecaRevitUtilities.ElementUtilities;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class PowerBIM_CircuitRoute
    {
        public PowerBIM_ProjectInfo projInfo { get; set; }
        public PowerBIM_CircuitData Circuit { get; set; }


        public double Length_To_First { get; set; }
        public double Length_Total { get; set; }



        public string Circuit_Path_Mode { get; set; }

        public PowerBIM_CircuitRoute(PowerBIM_ProjectInfo pi, PowerBIM_CircuitData CCT)
        {
            projInfo = pi;
            Circuit = CCT;
        }

        /// <summary>
        /// uses revit paths feature to calculate length of a circuit
        /// </summary>
        /// <param name="DBxyz"></param>
        /// 

        private double GetExtraLengthPerElementAdjustmentFactor()
        {
            if (PowerBIM_5.Properties.Settings.Default.SettingsControlledGlobally)
            {
                return Circuit.CCT_Is_Lighting ? projInfo.Length_ExtraPerElement_Lighting :
                       Circuit.CCT_Is_Power ? projInfo.Length_ExtraPerElement_Power :
                       projInfo.Length_ExtraPerElement_Other;
            }
            else
            {
                return Circuit.CCT_Is_Lighting ? Circuit.DB.Length_ExtraPerElement_Lighting :
                       Circuit.CCT_Is_Power ? Circuit.DB.Length_ExtraPerElement_Power :
                       Circuit.DB.Length_ExtraPerElement_Other;

            }
        }

        private double GetExtraLengthPerCircuitAdjustmentFactor()
        {
            if (PowerBIM_5.Properties.Settings.Default.SettingsControlledGlobally)
            {
                return Circuit.CCT_Is_Lighting ? projInfo.Length_ExtraPerCCT_Lighting :
                       Circuit.CCT_Is_Power ? projInfo.Length_ExtraPerCCT_Power :
                       projInfo.Length_ExtraPerCCT_Other;
            }
            else
            {
                return Circuit.CCT_Is_Lighting ? Circuit.DB.Length_ExtraPerCCT_Lighting :
                       Circuit.CCT_Is_Power ? Circuit.DB.Length_ExtraPerCCT_Power :
                       Circuit.DB.Length_ExtraPerCCT_Other;
            }
        }


        public void CCT_CalculateCircuitLength(/*out List<string> BadCircuits*/)
        {
            //BadCircuits = new List<string>();

            double dblLen_1stElem = 0;
            double dblLen_Total = 0;

            double extraLengthPerElement = GetExtraLengthPerElementAdjustmentFactor();
            double extraLengthPerCircuit = GetExtraLengthPerCircuitAdjustmentFactor();

            //fetch revit length recorded and convert to mm
            double revitLength = RevitUnitConvertor.InternalToMm(Circuit.CCT_Electrical_System.Length);


            IList<XYZ> ListOfPathNodes = Circuit.CCT_Electrical_System.GetCircuitPath();   //list of revit path node coordinates
            ElementSet CircuitElems = Circuit.CCT_Electrical_System.Elements;
            XYZ firstElementXYZ = null;

            foreach (XYZ point in ListOfPathNodes)// Go through to find first element on the circuit path
            {
                foreach (Element rqCTTelem in CircuitElems)
                {
                    // Get box around the element
                    BoundingBoxXYZ elemBounds = rqCTTelem.get_BoundingBox(null);

                    //find the first element that is on the revit path         
                    if (BoundingBoxXYZContains(elemBounds, point))
                    {
                        firstElementXYZ = point;
                        break;
                    }
                }
                if (firstElementXYZ != null)//if first element has been located the loop can end
                {
                    break;
                }
            }

            //
            // --------- no elements on path so length to first is length to final ---------
            //

            if (firstElementXYZ == null)
            {
                //BadCircuits.Add(Circuit.CCT_Number); // Add wrong path as bad circuit

                dblLen_Total = RevitUnitConvertor.InternalToMm(Circuit.CCT_Electrical_System.Length) * (1 + extraLengthPerCircuit) + projInfo.GUI_Calc_Lengths_Extra_Termination; //uses the internal revit variable for length to be certain

                if (dblLen_Total < PowerBIM_Constants.Length_IsInternalThreshold)//if length is less than 5m length is negligible so set to zero
                {
                    dblLen_Total = 0;
                }
                else
                {
                    //Add extra length for each circuit element
                    dblLen_Total = dblLen_Total + (CircuitElems.Size * extraLengthPerElement);
                }

                //Write back to global parameters
                Length_To_First = dblLen_Total;
                Length_Total = dblLen_Total;

            }

            //
            // ---------- NORMAL CASE ---------------
            //
            else
            {
                //compute length to first by going along path and recording distances
                XYZ prevPoint = Circuit.DB.DB_Location;//first node is DB

                foreach (XYZ point in ListOfPathNodes)
                {
                    dblLen_1stElem += prevPoint.DistanceTo(point);
                    if (firstElementXYZ.IsAlmostEqualTo(point)) //save the length it took to get to the first element 
                    {
                        break;
                    }
                    prevPoint = point; // set this point as new previous point 
                }

                //** 1.44 If the total length is too short <0.5m, set both lengths to zero
                if (revitLength < PowerBIM_Constants.Length_IsInternalThreshold)
                {
                    dblLen_Total = 0;
                    dblLen_1stElem = 0;
                }
                else
                {
                    //Add termination length 
                    dblLen_Total = revitLength + projInfo.GUI_Calc_Lengths_Extra_Termination;
                    dblLen_1stElem = RevitUnitConvertor.InternalToMm(dblLen_1stElem) + projInfo.GUI_Calc_Lengths_Extra_Termination;

                    //Add extra length for each circuit element
                    dblLen_Total = dblLen_Total + (CircuitElems.Size * extraLengthPerElement);
                    dblLen_1stElem = dblLen_1stElem + extraLengthPerElement;

                    //Add safety factor
                    dblLen_Total = dblLen_Total * (1 + extraLengthPerCircuit);
                    dblLen_1stElem = dblLen_1stElem * (1 + extraLengthPerCircuit);
                }

                // if only one element on circuit then set length to first = to lnegth to final)
                if(CircuitElems.Size == 1)
                {
                    dblLen_1stElem = dblLen_Total;
                }

                // Write back to global parameters
                Length_To_First = dblLen_1stElem;
                Length_Total = dblLen_Total;

            }
        }




        public bool BoundingBoxXYZContains(BoundingBoxXYZ bb, XYZ p)
        {
            //check if point is greater than the minimum extent of the box and less than the maximum extent 
            double tolerance = RevitUnitConvertor.MmToInternal(projInfo.NodeCircuitPathTolerance);

            if (((bb.Min.X - tolerance) < p.X) && ((bb.Min.Y - tolerance) < p.Y))//is point greater than min bounds of box
            {
                if (((bb.Max.X + tolerance) > p.X) && ((bb.Max.Y + tolerance) > p.Y))//is point less than max bounds of box
                {
                    return true;
                }
            }
            return false;

        }

        public void UpdateRevitPathMode(bool pathModeChangedFromEditCircuit, out string lockedUserName)
        {
            lockedUserName = string.Empty;

            //uses the Circuit_Path_mode string to update the path in revit
            // Check element's owner
            if ((!Circuit.CCT_Electrical_System.IsLocked(projInfo.Document) || Circuit.CCT_Electrical_System.ElementOwner(projInfo.Document) == "") && pathModeChangedFromEditCircuit)
            {
                //uses the Circuit_Path_mode string to update the path in revit
                using (var Trans_CCT = new SubTransaction(projInfo.Document))
                {
                    Trans_CCT.Start();
                    Circuit.CCT_Electrical_System.CircuitPathMode = (Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode)Enum.Parse(typeof(Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode), Circuit_Path_Mode, true); //converts string into revit's enum to set revit path mode
                    Trans_CCT.Commit();
                }
                projInfo.Document.Regenerate();
            }
            if (Circuit.CCT_Electrical_System.IsLocked(projInfo.Document) && Circuit.CCT_Electrical_System.ElementOwner(projInfo.Document) != "")
            {
                lockedUserName = Circuit.CCT_Electrical_System.ElementOwner(projInfo.Document);
            }

        }

        public bool OpenPathCustomisingView()
        {

            PowerBIM_3DViewLogic ThreeDLogic = new PowerBIM_3DViewLogic(projInfo); //initialize logic for creating 3D view

            double buffer = RevitUnitConvertor.MmToInternal(4000);

            IList<XYZ> ListOfPathNodes = Circuit.CCT_Electrical_System.GetCircuitPath();   //list of revit path node coordinates

            Autodesk.Revit.DB.ElementSet CircuitElems = Circuit.CCT_Electrical_System.Elements;
            List<Autodesk.Revit.DB.Element> elements = (from Autodesk.Revit.DB.Element e in CircuitElems
                                                        select e).ToList(); //convert revit element set into list of elements
            BoundingBoxCalculator bound = BoundingBoxCalculator.Create(projInfo.Document, ListOfPathNodes, projInfo.TaskLogger);

            if (bound != null)
            {
                //find template by name using LINQ
                View viewTemplate = (from v in new FilteredElementCollector(projInfo.Document).OfClass(typeof(View)).Cast<View>()
                                     where v.IsTemplate == true && v.Name.Equals("WR_77_3D_POWERBIM")
                                     select v).SingleOrDefault();

                if (viewTemplate == null)
                {
                    UI.Forms.ModelessPowerBIM_CircuitEditEnhancedFormHandler.ShowMsgToTheUser("PowerBIM needs a view template to create a 3D view.\n\nPlease create a new 3D view Template name 'WR_77_3D_POWERBIM' and re-launch PowerBIM", "Missing view template");
                   
                    return false;
                }

                //if view exists edit it to suit circuit. if it does not create a new one from scratch
                if (ThreeDLogic._ViewExists)
                {
                    PowerBIM_3DViewLogic.RunLogic(projInfo.UIDocument, ThreeDLogic._PB3DView as View3D, buffer, bound, false, projInfo.TaskLogger);//this modifies an already existing view 
                    return true;
                }
                else
                {
                    PowerBIM_3DViewLogic.RunLogic(projInfo.UIDocument, ThreeDLogic._UserViewName, viewTemplate as View, buffer, bound, false, projInfo.TaskLogger); //this creates a new view 
                    return true;
                }
            }
            else
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "Failed to create 3D View: bounding box is null.");
                return false;
            }

        }

    }
}


