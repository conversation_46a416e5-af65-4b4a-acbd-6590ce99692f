﻿using MEP.PowerBIM_5.CoreLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_1._5.UI.UiData
{
    class dgvDBSummarydata
    {
        #region Properties

        public string DB_Name { get; set; }
        public int DB_PassCount { get; set; }
        public int DB_WarningCount { get; set; }
        public int DB_FailCount { get; set; }
        public string DB_UserNotes { get; set; }
        public string DB_Notes { get; set; }
        public bool DB_UpdateRequired { get; set; }

        #endregion

        #region Constructors

        public dgvDBSummarydata(PowerBIM_DBData DB)
        {
            // Inputs
            DB_Name = DB.Schedule_DB_Name;
            DB_PassCount = DB.Result_PassCount;
            DB_WarningCount = DB.Result_WarningCount;
            DB_FailCount = DB.Result_FailCount;
            DB_UserNotes = DB.User_Notes;
            DB_Notes = DB.GUI_Notes;
            DB_UpdateRequired = DB.Update_Required;
        }

        #endregion
    }
}