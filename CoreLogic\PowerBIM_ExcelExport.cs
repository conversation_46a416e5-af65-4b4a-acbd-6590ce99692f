﻿using Autodesk.Revit.UI;
using Common.Utilities;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Excel = Microsoft.Office.Interop.Excel;
using System.Windows.Forms;
using Common.UI.Forms;
using Autodesk.Revit.DB.Electrical;
using Common.Extensions;
using Microsoft.Office.Interop.Excel;
using Autodesk.Revit.DB;
using System.Text.RegularExpressions;
using Common.ExcelInterop;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_5.UI.Forms;
using DocumentFormat.OpenXml.Packaging;
using Common.OpenXML;
using Common.OpenXML.Macros;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml;

namespace MEP.PowerBIM_5.CoreLogic
{
    //
    // This module handles the two file export functions from PowerBIM. 
    //
    //  1) DB Schedule
    //      - creates an excel file mimicing the PowerBIM Validation Template
    //      
    //  2) Verification Schedule
    //      - creates an excel file mimicing the PowerBIM DB Issue templates. 
    //     


    class PowerBIM_ExcelExport
    {
        public PowerBIM_ProjectInfo projInfo { get; set; }

        //
        // Constructors
        //
        public PowerBIM_ExcelExport(PowerBIM_ProjectInfo pi)
        {
            projInfo = pi;
        }

        /// <summary>
        /// Recalculates all circuit data to ensure fresh EFLI and voltage drop values before export
        /// This matches the same process that happens in the Enhanced Circuit Edit form
        /// </summary>
        private void RecalculateAllCircuitData(List<PowerBIM_DBData> DBs)
        {
            int totalCircuits = DBs.Sum(db => db.CCTs.Count);
            string progressMessage = "{0} of " + totalCircuits.ToString() + " circuits recalculated...";
            string caption = "Recalculating circuit data for export";

            using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, totalCircuits))
            {
                foreach (var DB in DBs)
                {
                    foreach (var circuit in DB.CCTs)
                    {
                        pf.Step($"Recalculating circuit {circuit.CCT_Number} on {DB.Schedule_DB_Name}...");

                        // Refresh derived circuit properties (same as Enhanced Circuit Edit form)
                        circuit.Refresh_DerrivedCircuitProperties();

                        // Run PowerBIM check to recalculate EFLI and voltage drop values
                        circuit.RunPowerBIMCheck();

                        pf.Increment();
                    }
                }
            }
        }

        internal void Export_DBSCheduleWorkbook(List<PowerBIM_DBData> DBs, bool ShowVerification, bool includeCircuitPathImages)
        {
            // Recalculate all circuit data to ensure fresh EFLI and voltage drop values
            RecalculateAllCircuitData(DBs);

            if (!ExportWithManualBackup(DBs, ShowVerification, includeCircuitPathImages, false))
            {
                MessageBox.Show("Fail to export 1");
                if (!ExportWithManualBackup(DBs, ShowVerification, includeCircuitPathImages, true))
                {
                    MessageBox.Show("Fail to export 2");
                }
            }

        }

        private bool ExportWithManualBackup(List<PowerBIM_DBData> DBs, bool ShowVerification, bool includeCircuitPathImages, bool withBackup)
        {
            try
            {
                //
                // Export PowerBIM DB Schedules
                //

                // Generating a progress bar for the export so the user can see progress.
                //
                PB_ProgressBar pb = new PB_ProgressBar();
                pb.Reset();
                pb.SetScope(8, 1);
                pb.SetTask("Exporting", 1);
                pb.SetCaption("Export - Initialising ...", 1);
                pb.LevelUp();


                // Configuring the progress bar.
                pb.Reset(pb.Level + 1);
                pb.SetCaption("Initialising ...", pb.Level + 1);
                pb.LevelUp();

                // Setting the scope of the progress bar.
                pb.SetScope(9 + DBs.Count, pb.Level);

                // Generating new date tme stamps
                pb.Step("Generating new date:time stamps ...");
                projInfo.GetDateTimeInfo();

                string path;
                if (ShowVerification)
                    path = projInfo.ReportFile_VerificationSummary_XLS;
                else
                    path = projInfo.ReportFile_CableSummary_XLS;

                if (!withBackup)
                {
                    // Copying the template file to the output location.
                    pb.Step("Creating copy of template file ...");
                    try
                    {
                        File.Copy(PowerBIM_Constants.Default_DBTemplatePath_MacrosDisabled, path);
                    }
                    catch (Exception e)
                    {
                        MessageBox.Show(e.Message, "Template copy error");
                    }

                }
                else
                {
                    #region Check if it's working on manually copied excel file
                    DialogResult dr = MessageBox.Show("There is a problem with current Excel.\n You can either roll back your excel version to 2208 or attempt to create the file manually\nPlease copy PowerBIM template manually.\n\nClick [Yes] if you have already copied the template\nor\n[No] if you haven't",
                              "Excel exception", MessageBoxButtons.YesNo);
                    switch (dr)
                    {
                        case DialogResult.Yes:
                            OpenFileDialog openDialog = new OpenFileDialog();
                            openDialog.Title = "Select the copied template";
                            openDialog.Filter = "Excel Files (*.xlsm)|*.xlsm";
                            if (openDialog.ShowDialog() == DialogResult.OK)
                            {
                                path = openDialog.FileName;
                            }
                            break;
                        case DialogResult.No:
                            pb.Close();
                            return false;
                    }
                    #endregion
                }

                // Opening the output file.
                pb.Step("Opening the new workbook ...");
                using (var document = SpreadsheetDocument.Open(path, true))
                {
                    OpenXMLUtilities.DeleteCalculationChainPart(document);

                    // Defining the worksheets (we need to modify 'Calculation' and 'Luminaire Input').
                    pb.Step("Initialising workbook ...");

                    var templateSheetName = "Template";
                    var summarySheetName = "Summary";
                    var instructionsSheetName = "Instructions";
                    var PBSettingsSheetName = "PB Settings";

                    // Updating the project information.
                    pb.Step("Adding project header information...");

                    // Updating the project information in "Instructions" sheet
                    OpenXMLUtilities.UpdateCell(document, instructionsSheetName, $"C{PowerBIM_Constants.Excel_JobNameRow}", projInfo.Document.ProjectInformation.get_Parameter(BuiltInParameter.PROJECT_NAME)?.AsString());
                    OpenXMLUtilities.UpdateCell(document, instructionsSheetName, $"C{PowerBIM_Constants.Excel_JobNumberRow}", projInfo.Document.ProjectInformation.get_Parameter(BuiltInParameter.PROJECT_NUMBER)?.AsString());
                    OpenXMLUtilities.UpdateCell(document, instructionsSheetName, $"C{PowerBIM_Constants.Excel_JobDateRow}", projInfo.Date);
                    OpenXMLUtilities.UpdateCell(document, instructionsSheetName, $"C{PowerBIM_Constants.Excel_EngineerRow}", projInfo.Engineer);
                    OpenXMLUtilities.UpdateCell(document, instructionsSheetName, $"C{PowerBIM_Constants.Excel_VerifierRow}", projInfo.Verifier);
                    OpenXMLUtilities.UpdateCell(document, instructionsSheetName, $"C{PowerBIM_Constants.Excel_RevisionRow}", projInfo.Revision);

                    // Filling PB Settings
                    pb.Step("Filling PB Settings...");
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_SystemMaxVoltDrop}", projInfo.System_VD_Max_Perc.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_DiscriminationTest}", $"{projInfo.Discrimination_Test_Multiplier}x");
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_CableDatabase}", projInfo.Cable_Database_Name);
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_ProtectiveDeviceProduct}", projInfo.CPD_Range_Selected);

                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_VoltDropCalculation_Lighting}", Enum.GetName(typeof(VoltDropCalculation), projInfo.LightingVDCalculation));
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_VoltDropCalculation_Power}", Enum.GetName(typeof(VoltDropCalculation), projInfo.PowerVDCalculation));
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_VoltDropCalculation_Other}", Enum.GetName(typeof(VoltDropCalculation), projInfo.OtherVDCalculation));
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_GPOSettings}", projInfo.Selected_GPO_Setting);
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_NonGPOPowerCircuitClearingTimes}", projInfo.Clearing_Time_Power.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_LightingCircuitClearingTimes}", projInfo.Clearing_Time_Lighting.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_ExtraLengthPerCircuitEnabled}", projInfo.Length_ExtraPerCCT_enabled.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_ExtraLengthPerCircuitLighting}", projInfo.Length_ExtraPerCCT_Lighting.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_ExtraLengthPerCircuitPower}", projInfo.Length_ExtraPerCCT_Power.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_ExtraLengthPerCircuitOther}", projInfo.Length_ExtraPerCCT_Other.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_ExtraLengthPerElementEnabled}", projInfo.Length_ExtraPerElement_enabled.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_ExtraLengthPerElementLighting}", projInfo.Length_ExtraPerElement_Lighting.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_ExtraLengthPerElementPower}", projInfo.Length_ExtraPerElement_Power.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_ExtraLengthPerElementOther}", projInfo.Length_ExtraPerElement_Other.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_PathNodeVsElementIntersection}", projInfo.NodeCircuitPathTolerance.ToString());
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_CableDatabaseLocation}", projInfo.Database_Path);
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_JobName}", projInfo.JobName);
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_JobNumber}", projInfo.JobNumber);
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_Date}", projInfo.Date);
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_Engineer}", projInfo.Engineer);
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_Verifier}", projInfo.Verifier);
                    OpenXMLUtilities.UpdateCell(document, PBSettingsSheetName, $"D{PowerBIM_Constants.Excel_Revision}", projInfo.Revision);

                    // DB counter
                    int dbCount = 0;
                    int incrementIndex = DBs.Count + 14;

                    List<string> Errors = new List<string>();

                    WorkbookPart workbookPart = document.WorkbookPart;
                    Sheet summarySheet = workbookPart.Workbook.Descendants<Sheet>().FirstOrDefault(s => s.Name == summarySheetName);
                    WorksheetPart summaryWorksheetPart = (WorksheetPart)workbookPart.GetPartById(summarySheet.Id);
                    DocumentFormat.OpenXml.Spreadsheet.Worksheet summaryWorksheet = summaryWorksheetPart.Worksheet;

                    for (int i = 0; i < DBs.Count - 1; i++)
                    {
                        OpenXMLUtilities.CopyAndInsertRow(summaryWorksheet, 15, incrementIndex);

                        if (i == 4)
                        {
                            OpenXMLUtilities.UnmergeCells(document, summarySheetName, "L19", "M19");
                            OpenXMLUtilities.UnmergeCells(document, summarySheetName, "L20", "M20");
                            OpenXMLUtilities.MergeCellsPB(document, summarySheetName, $"L{DBs.Count + 18}", $"M{DBs.Count + 18}");
                            OpenXMLUtilities.MergeCellsPB(document, summarySheetName, $"L{DBs.Count + 19}", $"M{DBs.Count + 19}");
                        }

                        incrementIndex--;
                    }

                    pb.LevelDown();

                    //clone worksheets for each DB in export
                    foreach (PowerBIM_DBData DB in DBs.AsEnumerable().Reverse())
                    {
                        //replace any / with | to make sure string is not going to cause error
                        string currentDBName = DB.Schedule_DB_Name.Replace("/", "|");

                        if (currentDBName.Length >= 31) //if string is too long shorten it
                        {
                            currentDBName = currentDBName.Substring(0, 30);
                            Errors.Add(" - file name had to be shortened (exeeded 31 characters)");
                        }

                        pb.Step("Cloning and renaming template worksheet");
                        OpenXMLUtilities.CloneWorksheet(document, templateSheetName, currentDBName);

                        pb.Step("Adding DB header information");
                        // Add DB header information
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"D{PowerBIM_Constants.Excel_HeaderRow_SwitchRating}", DB.Schedule_Main_Switch_Rating.ToString());
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"D{PowerBIM_Constants.Excel_HeaderRow_FeederCable}", DB.Schedule_Feeder_Cable);
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"D{PowerBIM_Constants.Excel_HeaderRow_Location}", DB.Schedule_Location);
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"D{PowerBIM_Constants.Excel_HeaderRow_Seismic}", DB.Schedule_Seismic_Category);
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"D{PowerBIM_Constants.Excel_HeaderRow_FaultRating}", DB.Schedule_Device_Fault_Rating);
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"E2", currentDBName);

                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"J{PowerBIM_Constants.Excel_HeaderRow_FormRating}", DB.Schedule_Form_Rating);
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"J{PowerBIM_Constants.Excel_HeaderRow_BusFault}", DB.Schedule_Bus_Fault_Level);
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"J{PowerBIM_Constants.Excel_HeaderRow_Surge}", DB.Schedule_Surge_Protection);
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"J{PowerBIM_Constants.Excel_HeaderRow_Metering}", DB.Schedule_Metering);
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"J{PowerBIM_Constants.Excel_HeaderRow_MaxVD}", (DB.Schedule_Final_Circuit_MaxVD * 100).ToString());

                        // add verifcation header info
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"P{PowerBIM_Constants.Excel_HeaderRow_SwitchRating}", DB.DB_Check_Result);
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"P{PowerBIM_Constants.Excel_HeaderRow_FeederCable}", projInfo.Ambient_Temp.ToString());
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"P{PowerBIM_Constants.Excel_HeaderRow_Location}", projInfo.System_VD_Max_Perc.ToString());
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"P{PowerBIM_Constants.Excel_HeaderRow_Seismic}", projInfo.Discrimination_Test_Multiplier.ToString());

                        // column names
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"X{PowerBIM_Constants.Excel_HeaderRow_FormRating}", DB.EFLI_R.ToString());
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"X{PowerBIM_Constants.Excel_HeaderRow_BusFault}", DB.EFLI_X.ToString());
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"X{PowerBIM_Constants.Excel_HeaderRow_Surge}", DB.DBVD.ToString());
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"X{PowerBIM_Constants.Excel_HeaderRow_Metering}", DB.PSCC.ToString());
                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"X{PowerBIM_Constants.Excel_HeaderRow_MaxVD}", DB.Upstream_Device_Rating.ToString());

                        // Add each circuit entry.
                        pb.Step("Beginning to write circuit row entries..");
                        var lastCellNumberToWrite = PowerBIM_Constants.Excel_DB_StartRow + DB.CCTs.Count;

                        if (DB.PanelScheduleView != null)
                        {
                            var excelRow = PowerBIM_Constants.Excel_DB_StartRow;
                            var addRowsForMergeCell = 2;
                            string nextCircuitNumber = "";

                            pb.Reset();
                            pb.SetScope(lastCellNumberToWrite, 1);

                            for (int i = 0; i < lastCellNumberToWrite; i++)
                            {
                                // Get circuit (PowerBIM_CircuitData)
                                var cct = DB.CCTs.Find(x => x.CCT_Number == DB.PanelScheduleView.GetCircuitByCell(i, 1)?.CircuitNumber);
                                if (cct != null)
                                {
                                    pb.Step($"Writing '{DB.Schedule_DB_Name}' circuit '{cct.CCT_Number}' information...");
                                    // If 3 poles > merge next 3 rows, extract and change circuit number for each row
                                    if (cct.Number_Of_Poles == 3)
                                    {
                                        // Writing 3-pole circuit information
                                        WriteToOpenXml(document, currentDBName, cct, excelRow, true, includeCircuitPathImages);

                                        for (int j = 2; j <= 34; j++) // 27 is Column AA
                                        {
                                            OpenXMLUtilities.MergeCells(document, currentDBName, $"{OpenXMLUtilities.GetColumnName(j)}{excelRow}", $"{OpenXMLUtilities.GetColumnName(j)}{excelRow + 2}");
                                        }

                                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"{OpenXMLUtilities.GetColumnName(2)}{excelRow}", Extract3PolesNaming(cct.CCT_Number, 0));
                                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"{OpenXMLUtilities.GetColumnName(2)}{excelRow + 1}", Extract3PolesNaming(cct.CCT_Number, 1));
                                        OpenXMLUtilities.UpdateCell(document, currentDBName, $"{OpenXMLUtilities.GetColumnName(2)}{excelRow + 2}", Extract3PolesNaming(cct.CCT_Number, 2));

                                        nextCircuitNumber = Extract3PolesNaming(cct.CCT_Number, 2);
                                        excelRow += addRowsForMergeCell + 1;
                                        i += addRowsForMergeCell;
                                    }
                                    else
                                    {
                                        // Writing normal circuit information (single pole)
                                        WriteToOpenXml(document, currentDBName, cct, excelRow, false, includeCircuitPathImages);
                                        nextCircuitNumber = cct.CCT_Number;
                                        excelRow++;
                                    }
                                }
                            }

                            // Add rows for the rest of the panel size
                            for (int i = excelRow; i < excelRow + ((DB.NumberOfWays * 3) - (excelRow - PowerBIM_Constants.Excel_DB_StartRow)); i++)
                            {
                                pb.Step($"{DB.Schedule_DB_Name} - Writing the rest of the panel size rows...");
                                nextCircuitNumber = GetNextCircuitNumber(nextCircuitNumber);
                                OpenXMLUtilities.UpdateCell(document, currentDBName, $"A{i}", nextCircuitNumber);  // A is the CCT number column
                            }
                        }

                        // Hide excess rows by updating the Number of Ways
                        pb.Step("Hiding excess rows...");
                        if (DB.NumberOfWays == 0)
                        {
                            OpenXMLUtilities.UpdateCell(document, currentDBName, $"{PowerBIM_Constants.Excel_NumberWaysCol}{PowerBIM_Constants.Excel_NumberWaysRow}", "36");
                        }
                        else
                        {
                            OpenXMLUtilities.UpdateCell(document, currentDBName, $"{PowerBIM_Constants.Excel_NumberWaysCol}{PowerBIM_Constants.Excel_NumberWaysRow}", DB.NumberOfWays.ToString());
                        }

                        // Placeholder to run the macro for updating DB size
                        MacroExecutor.RunMacro(document, PowerBIMMacroNames.Template_UpdateDBSize_Click, new PowerBIMMacroParams { SheetName = currentDBName, NumOfWays = DB.NumberOfWays });

                        // Placeholder to run the macro for hide/show verification data
                        pb.Step("Hide/show verification data...");
                        MacroExecutor.RunMacro(document, PowerBIMMacroNames.Template_UpdateDBSize_Click, new PowerBIMMacroParams { SheetName = currentDBName, NumOfWays = DB.NumberOfWays });

                        // If verification output should not be shown, run the hide macro
                        if (!ShowVerification)
                        {
                            MacroExecutor.RunMacro(document, PowerBIMMacroNames.Template_ShowHideVerification_Click, new PowerBIMMacroParams { SheetName = currentDBName, NumOfWays = DB.NumberOfWays });
                        }

                        // === Writing to the Summary Sheet ===

                        // Determine the target row for insertion dynamically
                        int currentRow = PowerBIM_Constants.Excel_Summary_StartRow + dbCount;
                        pb.Step($"Adding new DB entry to summary sheet for {currentDBName}...");

                        // Add the DB name (this will pull through relevant data using Excel formulas)
                        OpenXMLUtilities.UpdateCell(document, summarySheetName, $"{OpenXMLUtilities.GetColumnName(PowerBIM_Constants.Excel_Summary_DBNameCol)}{currentRow}", currentDBName);

                        // Manually write circuit results
                        var dBsFlipped = DBs.AsEnumerable().Reverse().ToList();

                        // Insert values as integer for this UpdateCell
                        OpenXMLUtilities.UpdateCell(document, summarySheetName, $"{OpenXMLUtilities.GetColumnName(PowerBIM_Constants.Excel_Summary_CCTPassCol)}{currentRow}", dBsFlipped[dbCount].Result_PassCount);
                        OpenXMLUtilities.UpdateCell(document, summarySheetName, $"{OpenXMLUtilities.GetColumnName(PowerBIM_Constants.Excel_Summary_CCTWarningCol)}{currentRow}", dBsFlipped[dbCount].Result_WarningCount);
                        OpenXMLUtilities.UpdateCell(document, summarySheetName, $"{OpenXMLUtilities.GetColumnName(PowerBIM_Constants.Excel_Summary_CCTFailCol)}{currentRow}", dBsFlipped[dbCount].Result_FailCount);

                        UpdateDBPassResult(document, dBsFlipped[dbCount].Result_FailCount, summaryWorksheet, currentRow);

                        //Increment the DB counter
                        dbCount++;
                    }

                    var totalCircuitRow = PowerBIM_Constants.Excel_Summary_StartRow + dbCount + 4;

                    // Update Total formulas in summary sheet
                    UpdateSummaryTotalFormulas(dbCount, summaryWorksheet, totalCircuitRow);

                    // Update conditional formatting for the summary sheet
                    UpdateSummaryConditionalFormatting(document, summaryWorksheet, totalCircuitRow);

                    // Set the print area for the worksheet
                    OpenXMLUtilities.SetPrintArea(workbookPart, summarySheet, "A1", $"S{DBs.Count + 20}");

                    // Completed.
                    pb.Step("Completed");

                    // Handle Errors
                    if (Errors.Count == 0)
                    {
                        ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Export Complete", " Export completed successfully");
                    }
                    else
                    {
                        Errors.Insert(0, "The export completed with the following warnings: \n");
                        string Message = string.Join("\n", Errors);
                        using (BecaBaseMessageForm becaBaseMessageForm = new BecaBaseMessageForm(Message, "Export Complete"))
                        {
                            becaBaseMessageForm.TopLevel = true;
                            becaBaseMessageForm.TopMost = true;
                            becaBaseMessageForm.ShowDialog();
                        }
                    }

                    pb.LevelDown();
                    pb.Close();
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }

        }

        private static void UpdateSummaryTotalFormulas(int dbCount, DocumentFormat.OpenXml.Spreadsheet.Worksheet summaryWorksheet, int totalCircuitRow)
        {
            // Calculate the dynamic end row for formulas based on Excel_Summary_StartRow and dbCount
            int dynamicRowEnd = PowerBIM_Constants.Excel_Summary_StartRow + dbCount;

            // Update the Total Pass formula: SUM(O14:O{dynamicRowEnd})
            Cell totalPassCell = OpenXMLUtilities.GetOrCreateCell(summaryWorksheet, $"O{totalCircuitRow}");
            if (totalPassCell.CellFormula != null)
            {
                totalPassCell.CellFormula.Text = $"SUM(O14:O{dynamicRowEnd})";
            }

            // Update the Total Fail formula: SUM(P14:P{dynamicRowEnd})
            Cell totalFailCell = OpenXMLUtilities.GetOrCreateCell(summaryWorksheet, $"P{totalCircuitRow}");
            if (totalFailCell.CellFormula != null)
            {
                totalFailCell.CellFormula.Text = $"SUM(P14:P{dynamicRowEnd})";
            }

            // Update the Total Warning formula: SUM(Q14:Q{dynamicRowEnd})
            Cell totalWarningCell = OpenXMLUtilities.GetOrCreateCell(summaryWorksheet, $"Q{totalCircuitRow}");
            if (totalWarningCell.CellFormula != null)
            {
                totalWarningCell.CellFormula.Text = $"SUM(Q14:Q{dynamicRowEnd})";
            }

            // Update the Total Circuit formula: O{totalCircuitRow}+P{totalCircuitRow})
            Cell totalCircuitCell = OpenXMLUtilities.GetOrCreateCell(summaryWorksheet, $"L{totalCircuitRow}");
            if (totalCircuitCell.CellFormula != null)
            {
                totalCircuitCell.CellFormula.Text = $"O{totalCircuitRow}+P{totalCircuitRow}";
            }

            // Update the Project Pass formula: IF(COUNTIF(R14:R{dynamicRowEnd},"NO") > 0, "NO", "YES")
            Cell projectPassCell = OpenXMLUtilities.GetOrCreateCell(summaryWorksheet, $"R{totalCircuitRow}");
            if (projectPassCell.CellFormula != null)
            {
                projectPassCell.CellFormula.Text = $"IF(COUNTIF(R14:R{dynamicRowEnd},\"NO\")>0, \"NO\", \"YES\")";
            }

            summaryWorksheet.Save();
        }

        private void UpdateDBPassResult(SpreadsheetDocument document, int failCount, DocumentFormat.OpenXml.Spreadsheet.Worksheet summaryWorksheet, int currentRow)
        {
            Cell cell = OpenXMLUtilities.GetOrCreateCell(summaryWorksheet, $"R{currentRow}");

            if (cell.CellFormula != null)
            {
                cell.CellFormula.Text = $"IF(P{currentRow}=\"\",\"NO DATA\",IF(P{currentRow}=0,\"YES\",\"NO\"))";
                summaryWorksheet.Save();
            }
        }

        public static void UpdateSummaryConditionalFormatting(SpreadsheetDocument document, DocumentFormat.OpenXml.Spreadsheet.Worksheet summaryWorksheet, int rowNumber)
        {
            // Get all the conditional formatting rules in the worksheet
            var conditionalFormattingElements = summaryWorksheet.Descendants<ConditionalFormatting>();

            foreach (var conditionalFormatting in conditionalFormattingElements)
            {
                var sequenceOfReferences = conditionalFormatting.SequenceOfReferences;

                if (sequenceOfReferences != null)
                {
                    foreach (var reference in sequenceOfReferences.Items)
                    {
                        // Check if the current range starts with "R14:"
                        if (reference.Value.StartsWith("R14:"))
                        {
                            // Extract the starting part ("R14") and adjust the ending part dynamically
                            string newSqref = $"R14:R{rowNumber}";
                            reference.Value = newSqref; // Update the range
                        }
                    }
                }
            }

            // Save the worksheet after modification
            summaryWorksheet.Save();
        }


        private void WriteToOpenXml(SpreadsheetDocument document, string sheetName, PowerBIM_CircuitData cCT, int row, bool isThreePhase, bool includeCircuitPathImages)
        {
            // Write circuit values to OpenXML cells

            // For three poles circuit, numbers are handled using Extract3PolesNaming
            if (!isThreePhase)
            {
                OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTNumber}{row}", cCT.CCT_Number.ToString());
            }

            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTTripRating}{row}", cCT.Breaker.Schedule_Trip_Rating.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTCurveType}{row}", cCT.Breaker.Schedule_Curve_Type?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTProtectiveDevice}{row}", cCT.Breaker.Schedule_Protective_Device?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTRCD}{row}", cCT.Schedule_RCD?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTOtherCOntrols}{row}", cCT.Schedule_Other_Controls?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTCable1}{row}", cCT.Cable_To_First.Cable_Name?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTCable2}{row}", cCT.Cable_To_Final.Cable_Name?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTDesignLength}{row}", Math.Round(cCT.LengthClass.Length_Total / 1000).ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTDescription}{row}", cCT.Schedule_Description?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTRevision}{row}", cCT.Schedule_Revision?.ToString());

            // Write verification results
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckTripRating}{row}", cCT.Schedule_CCTCheck_1_Data?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckCable1Valid}{row}", cCT.Schedule_CCTCheck_2_CableToFirst?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckCable2Valid}{row}", cCT.Schedule_CCTCheck_3_CableToFinal?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckCPDDiscrim}{row}", cCT.Schedule_CCTCheck_4_Discrimination?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckLoadCurrent}{row}", cCT.Schedule_CCTCheck_5_BreakerCurrent?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckCable1Current}{row}", cCT.Schedule_CCTCheck_6_CableToFirstCurrent?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckCable2Current}{row}", cCT.Schedule_CCTCheck_7_CableToFinalCurrent?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckEFLI}{row}", cCT.Schedule_CCTCheck_8_EFLI?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckFinalVD}{row}", cCT.Schedule_CCTCheck_9_FinalCircuitVD?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckSystemVD}{row}", cCT.Schedule_CCTCheck_10_SystemVD?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckSC1}{row}", cCT.Schedule_CCTCheck_11_CableToFirstSC?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckSC2}{row}", cCT.Schedule_CCTCheck_12_CableToFinalSC?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCT_CheckSummary}{row}", RemoveDuplicatesInCheckSummary(cCT.Schedule_CCTCheck_Summary));

            // Extra verification results
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTInstallMethod}{row}", cCT.Schedule_Install_Method?.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTRevitPathMode}{row}", cCT.CCT_Electrical_System.CircuitPathMode.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTLegnthFirst}{row}", Math.Round(cCT.LengthClass.Length_To_First / 1000, 2).ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTLengthTotal}{row}", Math.Round(cCT.LengthClass.Length_Total / 1000, 2).ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTDerating}{row}", cCT.Schedule_Derating_Factor.ToString());
            OpenXMLUtilities.UpdateCell(document, sheetName, $"{PowerBIM_Constants.Excel_Col_CCTDiversity}{row}", cCT.CCT_Diversity.ToString());

            if (includeCircuitPathImages)
            {
                // Placeholder for inserting circuit path images
                string rvtTitle = cCT.LengthClass.projInfo.Document.Title;
                rvtTitle = rvtTitle.Substring(0, rvtTitle.Length - 4);

                var dbRootFolderImages = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    "Beca MEP Tools", "PowerBIM Reports", rvtTitle, "Circuits paths images");

                var dbFolderImages = Path.Combine(dbRootFolderImages, sheetName); // sheet name is DB name
                string picturePath = Path.Combine(dbFolderImages, cCT.CCT_Number + ".png");

                if (File.Exists(picturePath))
                {
                    OpenXMLUtilities.InsertImageToCell(document, sheetName, PowerBIM_Constants.Excel_Col_CCTImage, row, picturePath, 40, 40);
                    // Adjust row height and column width if necessary
                    OpenXMLUtilities.SetRowHeightAndColumnWidth(document, sheetName, row, PowerBIM_Constants.Excel_Col_CCTImage);
                }
            }
        }

        /// <summary>
        /// This method will extract CircuitNumber for 3 poles circuit(ex. DB-140,41,42 || 43,44,45 || RWB14)
        /// It will devide the string to string array and returning any 3 index
        /// </summary>
        /// <param name="circuitNumber">RWB plus number</param>
        /// <param name="i">character index to get</param>
        /// <returns></returns>
        private static string Extract3PolesNaming(string circuitNumber, int i)
        {
            if (i > 2)
                return circuitNumber;

            var circuitNumberLowered = circuitNumber.ToLower();
            if (circuitNumberLowered.Contains("rwb"))
            {
                return circuitNumber[i] + ExtractNumberInString(circuitNumber).ToString();
            }
            else if (circuitNumber.Contains(","))
            {
                var split = circuitNumber.Split(',');
                if (Regex.IsMatch(split[0], @"^\d+$")) // if string is all numeric
                {
                    split = new string[] { split[0], (Convert.ToInt32(split[0]) + 1).ToString(), (Convert.ToInt32(split[0]) + 2).ToString() };
                }
                else
                {
                    split = new string[] { ReplaceNumberAndIncrement(split[0], 0), ReplaceNumberAndIncrement(split[0], 1), ReplaceNumberAndIncrement(split[0], 2) };
                }
                return split[i];
            }
            else
            {
                return circuitNumber;
            }
        }

        private static int ExtractNumberInString(string s)
        {
            return Convert.ToInt32(new String(s.Where(Char.IsDigit).ToArray()));
        }

        private static string ReplaceNumberAndIncrement(string s, int increment)
        {
            var number = ExtractNumberInString(s) + increment;
            return Regex.Replace(s, "[0-9]{2,}", number.ToString());
        }

        /// <summary>
        /// This method will get the next circuit number for the next cell in excel based on the
        /// last cell value written in excel (ex. R2, 2, DB1)
        /// </summary>
        /// <param name="s">input string</param>
        /// <returns></returns>
        private static string GetNextCircuitNumber(string s)
        {
            if (!string.IsNullOrWhiteSpace(s))
            {
                if (s.ToLower()[0].ToString() == "r" && string.Concat(s.Where(char.IsLetter)).Count() == 1)
                {
                    return "W" + ExtractNumberInString(s);
                }
                else if (s.ToLower()[0].ToString() == "b" && string.Concat(s.Where(char.IsLetter)).Count() == 1)
                {
                    return "R" + (ExtractNumberInString(s) + 1);
                }
                else if (s.ToLower()[0].ToString() == "w" && string.Concat(s.Where(char.IsLetter)).Count() == 1)
                {
                    return "B" + (ExtractNumberInString(s));
                }
                else if (Regex.IsMatch(s, @"^\d+$")) // string is all numeric (standard naming)
                {
                    return (Convert.ToInt32(s) + 1).ToString();
                }
                else if (string.Concat(s.Where(char.IsLetter)).Count() > 1 && string.Concat(s.Where(char.IsNumber)).Count() > 0) // by panel naming
                {
                    return ReplaceNumberAndIncrement(s, 1);
                }
                else
                {
                    return s;
                }
            }
            else
            {
                return s;
            }

        }

        public static string RemoveDuplicatesInCheckSummary(string input)
        {
            if (string.IsNullOrWhiteSpace(input)) return input; // Handle empty or null strings

            // Split the string by the delimiter " || "
            string[] parts = input.Split(new[] { " || " }, StringSplitOptions.None);

            // Use a List to build the unique parts while preserving the order
            List<string> uniqueParts = new List<string>();

            // Iterate over the split parts
            foreach (var part in parts)
            {
                // If the current part starts with "Warning:"
                if (part.TrimStart().StartsWith("Warning:", StringComparison.OrdinalIgnoreCase))
                {
                    // Check if a similar warning already exists in uniqueParts
                    int index = uniqueParts.FindIndex(p => p.TrimStart().StartsWith("Warning:", StringComparison.OrdinalIgnoreCase));
                    if (index != -1)
                    {
                        // Replace with the longer warning if there's an existing one
                        if (part.Length > uniqueParts[index].Length)
                        {
                            uniqueParts[index] = part;
                        }
                    }
                    else
                    {
                        // No similar warning found, add it to the list
                        uniqueParts.Add(part);
                    }
                }
                else
                {
                    // If it's not a warning, just add it directly if it's unique
                    if (!uniqueParts.Contains(part))
                    {
                        uniqueParts.Add(part);
                    }
                }
            }

            // Join the unique parts back together with " || " as the separator
            return string.Join(" || ", uniqueParts);
        }

    }
}
