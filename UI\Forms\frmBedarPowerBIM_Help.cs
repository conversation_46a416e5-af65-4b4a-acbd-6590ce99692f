﻿using Autodesk.Revit.UI;
using BecaRevitUtilities;
using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmBedarPowerBIM_Help : BecaBaseForm
    {
        string _documentPath;

        public frmBedarPowerBIM_Help()
        {
            InitializeComponent();

            _documentPath = @"C:\ProgramData\Autodesk\Revit\Addins\" + RevitVersionHelper.GetCurrentRevitVerision() + @"\Beca\Documentation\";

            LoadAgreement();
        }

        private void LoadAgreement()
        {
            if (Directory.Exists(_documentPath))
            {
                try
                {
                    string[] files = Directory.GetFiles(_documentPath, "*.rtf");
                    foreach (var fileName in files)
                    {
                        if (fileName.Contains("BEDAR Software Agreement"))
                        {
                            rtb_Agreement.LoadFile(fileName, RichTextBoxStreamType.RichText);
                            break;
                        }

                    }

                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{ex.Message}\nDocumentation folder: {_documentPath}");
                }
            }
        }

        private void btn_SheetCheat_Click(object sender, EventArgs e)
        {
            // URL of the website to open
            string url = "https://becagroup.sharepoint.com/sites/BIMBrilliance/SitePages/PowerBIM-5.aspx";

            // Open the URL in the default browser
            try
            {
                Process.Start(new ProcessStartInfo(url)
                {
                    UseShellExecute = true  // Ensures that the default browser is used
                });
            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., if no browser is available)
                MessageBox.Show($"Unable to open the help page. Error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
