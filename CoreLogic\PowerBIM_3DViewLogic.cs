﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitCommonAlgorithms.Interfaces;
using BecaRevitElementsCreators;
using BecaRevitUtilities.Collectors;
using BecaRevitUtilities.RevitViewsUtilities;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class PowerBIM_3DViewLogic
    {

        #region Fields

        public List<View> _threeDViews;
        public List<View> _threeDViewTemplates;
        public View _PB3DView;
        public View _PB3DViewTemplate;

        public PowerBIM_ProjectInfo projInfo { get; private set; }
        public bool _ViewExists { get; set; }
        public bool _ViewOpen { get; set; }
        public string _UserViewName { get; set; }

        #endregion

        #region Properties



        #endregion

        #region Constructors
        public PowerBIM_3DViewLogic(PowerBIM_ProjectInfo pi)
        {

            projInfo = pi;

            _UserViewName = GetUserViewName();
            ThreeDViewOptions_Load();
        }


        #endregion

        #region Methods

        public static string GetUserViewName()
        {
            return "PowerBIM_Path Length Editing View (3D) - " + System.Security.Principal.WindowsIdentity.GetCurrent().Name.Split('\\').Last(); //get users name into the view title

        }

        private void ThreeDViewOptions_Load()
        {
            _threeDViews = ElementCollectorUtility.GetViews(projInfo.UIDocument.Document, ViewType.ThreeD);
            // _threeDViewTemplates = ElementCollectorUtility.GetViewTemplates(projInfo.UIDocument.Document, ViewType.ThreeD);

            if (projInfo.UIDocument.ActiveView is View3D && projInfo.UIDocument.ActiveView.Name == _UserViewName)
                _ViewOpen = true;
            else
                _ViewOpen = false;


            if (_threeDViews.FindIndex(tdv => tdv.Name == _UserViewName) > 0)
            {
                _ViewExists = true;
                _PB3DView = _threeDViews.ElementAt(_threeDViews.FindIndex(tdv => tdv.Name == _UserViewName));
                //B3DView = _threeDViews[_threeDViews.FindIndex(tdv => tdv.Name == _UserViewName)];

            }
            else
            {
                _ViewExists = false;
                //_PB3DViewTemplate = _threeDViewTemplates.ElementAt(1);
            }
        }

        //logic for editing the already existing powerBIM 3D view for current circuit
        static public void RunLogic(UIDocument uiDoc, View3D view, double buffer, IBoundingBoxCalculator bbClacuator, bool isolateSelection, BecaActivityLoggerData logger)
        {
            using (Transaction tx = new Transaction(view.Document, BecaTransactionsNames.Easy3DView.GetHumanReadableString()))
            {
                tx.Start();

                ViewUtility.CropView3D(view, bbClacuator.BufferCalcBoundingBox(buffer));
                if (isolateSelection)
                {
                    view.IsolateElementsTemporary(bbClacuator.SelectedElementIds.ToList());
                    //uiDoc.Selection.SetElementIds(bbClacuator.SelectedElementIds.ToList());

                }
                uiDoc.Document.Regenerate();

                tx.Commit();
                //uiDoc.ActiveView = view;

            }
        }


        //logic for creating a powerBIM 3D view from scratch
        static public View RunLogic(UIDocument uiDoc, string viewName, View viewTemplate, double buffer, IBoundingBoxCalculator bbClacuator, bool isolateSelection, BecaActivityLoggerData logger)
        {
            View view = null;
            using (Transaction tx = new Transaction(uiDoc.Document, BecaTransactionsNames.Easy3DView.GetHumanReadableString()))
            {
                tx.Start();
                view = CreateNew3DView(uiDoc.Document, viewName, viewTemplate, bbClacuator, buffer, logger);

                if (isolateSelection)
                {
                    view.IsolateElementsTemporary(bbClacuator.SelectedElementIds.ToList());
                }

                uiDoc.Document.Regenerate();

                tx.Commit();
                uiDoc.ActiveView = view;


            }
            return view;
        }
        #region logic withoutTransactions

        //logic for editing the already existing powerBIM 3D view for current circuit
        static public void RunLogicWithoutTransactions(UIDocument uiDoc, View3D view, double buffer, IBoundingBoxCalculator bbClacuator, bool isolateSelection, BecaActivityLoggerData logger)
        {

            ViewUtility.CropView3D(view, bbClacuator.BufferCalcBoundingBox(buffer));
            if (isolateSelection)
            {
                view.IsolateElementsTemporary(bbClacuator.SelectedElementIds.ToList());
            }
        }


        //logic for creating a powerBIM 3D view from scratch
        static public void RunLogicWithoutTransactions(UIDocument uiDoc, string viewName, View viewTemplate, double buffer, IBoundingBoxCalculator bbClacuator, bool isolateSelection, BecaActivityLoggerData logger)
        {
            var view = CreateNew3DView(uiDoc.Document, viewName, viewTemplate, bbClacuator, buffer, logger);

            if (isolateSelection)
            {
                view.IsolateElementsTemporary(bbClacuator.SelectedElementIds.ToList());
            }
        }
        #endregion
        static View3D CreateNew3DView(Document doc, string viewName, View viewTemplat, IBoundingBoxCalculator bbClacuator, double buffer, BecaActivityLoggerData logger, bool showCutPlane = false)
        {
            View3D createdView = null;
            using (SubTransaction stx = new SubTransaction(doc))
            {
                View3DPropertisesOptions options = new View3DPropertisesOptions()
                {
                    ViewTemplatId = viewTemplat.Id,
                    ShowCutPlane = showCutPlane,
                    ViewSkecthPlane = bbClacuator.ViewSkecthPlane,
                    CropBox = bbClacuator.BufferCalcBoundingBox(buffer)
                };

                View3DCreator creator = new View3DCreator(options, logger);
                createdView = creator.Create(doc, viewName) as View3D;
            }

            return createdView;
        }

        public static View3D CreatePowerBimView(Document doc, BecaActivityLoggerData logger)
        {
            var viewName = PowerBIM_3DViewLogic.GetUserViewName();
            View viewTemplate = (from v in new FilteredElementCollector(doc).OfClass(typeof(View)).Cast<View>()
                                 where v.IsTemplate == true && v.Name.Equals("WR_77_3D_POWERBIM")
                                 select v).SingleOrDefault();
            if (viewTemplate == null)//if view template does not exist view cant be created
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "PowerBIM view could not be created because PowerBIM view template does not exist \n \n Please create a 3D view named WR_77_3D_POWERBIM and try again");

            }
            else
            {
                //create new view
                View3DPropertisesOptions vOptCreator = new View3DPropertisesOptions()
                {
                    ViewTemplatId = viewTemplate.Id,

                };
                View3DCreator creator = new View3DCreator(vOptCreator, logger);
                return creator.Create(doc, viewName) as View3D;
            }

            return null;
        }
        #endregion

    }
}