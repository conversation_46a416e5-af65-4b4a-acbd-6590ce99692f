﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using BecaActivityLogger.CoreLogic.Data;
using Common.UI.Forms;
using MEP.PowerBIM_1._5.UI.ModelessRevitForm;
using MEP.PowerBIM_5.CoreLogic;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmPowerBIM_DBSettings : BecaBaseForm
    {

        #region Fields

        private RequestHandler _handler;
        private ExternalEvent _externalEvent;

        private PowerBIM_DBData _db;
        private bool checkGPOCalc80perc = false;
        private bool checkGPOActualLoad = true;
        private bool checkGPO1000plus100 = false;
        private bool checkClearingTimePower04 = true;
        private bool checkClearingTimePower5 = false;
        private bool checkClearingTimeLighting04 = true;
        private bool checkClearingTimeLighting5 = false;

        private Parameter _dbParamPBGPOCalc;

        #endregion

        #region Properties



        #endregion

        #region Constructors

        public frmPowerBIM_DBSettings(ExternalEvent exEvent, RequestHandler handler, PowerBIM_DBData db)
        {
            InitializeComponent();

            _handler = handler;
            _externalEvent = exEvent;

            _db = db;
            this.TitleText = $"{db.Schedule_DB_Name} Settings";

            WriteSafetyFactorsToTextBox();

            if ((int)_db.PowerVDCalculation == 1)
                rb_PowerLinearDeprecating.Checked = true;
            else
                rb_PowerLumpLoad.Checked = true;

            if ((int)_db.LightingVDCalculation == 1)
                rb_LightingLinearDeprecating.Checked = true;
            else
                rb_LightingLumpLoad.Checked = true;

            if ((int)_db.OtherVDCalculation == 1)
                rb_OtherLinearDeprecating.Checked = true;
            else
                rb_OtherLumpLoad.Checked = true;

            //Set GPO setting from revit
            _dbParamPBGPOCalc = db.DB_Element.get_Parameter(PowerBIM_Constants.paramGuidPBGPOCalc);
            if (_dbParamPBGPOCalc.AsInteger() >= 1 && _dbParamPBGPOCalc.AsInteger() <= 100)
            {
                tb_VoltDropBreakerRatingPercent.Text = _dbParamPBGPOCalc.AsInteger().ToString();
                checkGPOCalc80perc = true;
                checkGPOActualLoad = false;
                checkGPO1000plus100 = false;
            }
            else if (_db.GPO_Calc_Integer == -2)
            {
                checkGPOCalc80perc = false;
                checkGPOActualLoad = true;
                checkGPO1000plus100 = false;
            }
            else if (_db.GPO_Calc_Integer == -1)
            {
                checkGPOCalc80perc = false;
                checkGPOActualLoad = false;
                checkGPO1000plus100 = true;
            }

            //Set clearing time settings from revit
            if (_db.Clearing_Time_Power == 400)
                checkClearingTimePower04 = true;
            else if (_db.Clearing_Time_Power == 5000)
                checkClearingTimePower5 = true;

            //Set clearing time settings from revit
            if (_db.Clearing_Time_Lighting == 400)
                checkClearingTimeLighting04 = true;
            else if (_db.Clearing_Time_Lighting == 5000)
                checkClearingTimeLighting5 = true;

            radioButtonGPOCalcBreakerRating.Checked = checkGPOCalc80perc;
            radioButtonGPOCalcAssignedLoad.Checked = checkGPOActualLoad;
            radioButtonGPOCalc1000plus100.Checked = checkGPO1000plus100;

            radioButtonNonGPO04.Checked = checkClearingTimePower04;
            radioButtonNonGPO5.Checked = checkClearingTimePower5;

            radioButtonLightingClearning04.Checked = checkClearingTimeLighting04;
            radioButtonLightingClearning5.Checked = checkClearingTimeLighting5;
        }

        private void WriteSafetyFactorsToTextBox()
        {
            // Lighting CCTs
            if (_db.Length_ExtraPerElement_Lighting == 0)
            {
                this.tbTxtLenPerElemLighting.Text = "00";
            }
            else
            {
                this.tbTxtLenPerElemLighting.Text = (_db.Length_ExtraPerElement_Lighting / 1000).ToString();
            }

            if (_db.Length_ExtraPerCCT_Lighting == 0)
            {
                this.tbTxtLenPerCCTLighting.Text = "00";
            }
            else
            {
                this.tbTxtLenPerCCTLighting.Text = (_db.Length_ExtraPerCCT_Lighting * 100).ToString();
            }

            // Power CCTs
            if (_db.Length_ExtraPerElement_Power == 0)
            {
                this.tbTxtLenPerElemPower.Text = "00";
            }
            else
            {
                this.tbTxtLenPerElemPower.Text = (_db.Length_ExtraPerElement_Power / 1000).ToString();
            }

            if (_db.Length_ExtraPerCCT_Power == 0)
            {
                this.tbTxtLenPerCCTPower.Text = "00";
            }
            else
            {
                this.tbTxtLenPerCCTPower.Text = (_db.Length_ExtraPerCCT_Power * 100).ToString();
            }

            // Other CCTs
            if (_db.Length_ExtraPerElement_Other == 0)
            {
                this.tbTxtLenPerElemOther.Text = "00";
            }
            else
            {
                this.tbTxtLenPerElemOther.Text = (_db.Length_ExtraPerElement_Other / 1000).ToString();
            }

            if (_db.Length_ExtraPerCCT_Other == 0)
            {
                this.tbTxtLenPerCCTOther.Text = "00";
            }
            else
            {
                this.tbTxtLenPerCCTOther.Text = (_db.Length_ExtraPerCCT_Other * 100).ToString();
            }
        }

        private VoltDropCalculation GetVoltDropCalculation(RadioButton linearDeprecatingRB, RadioButton lumpLoadRB)
        {
            if (linearDeprecatingRB.Checked)
            {
                return VoltDropCalculation.LinearDeprecating;
            }
            else if (lumpLoadRB.Checked)
            {
                return VoltDropCalculation.LumpLoad;
            }

            return VoltDropCalculation.Unknown; // Or throw an exception if neither RadioButton is checked
        }


        #endregion

        #region Methods

        #region Modeless Functionality

        /// <summary>
        ///   A private helper method to make a request
        ///   and put the dialog to sleep at the same time.
        /// </summary>
        /// <remarks>
        ///   It is expected that the process which executes the request 
        ///   (the Idling helper in this particular case) will also
        ///   wake the dialog up after finishing the execution.
        /// </remarks>
        ///
        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
            DozeOff();

        }

        /// <summary>
        ///   Control enabler / disabler 
        /// </summary>
        ///
        private void EnableCommands(bool status)
        {
            foreach (System.Windows.Forms.Control ctrl in this.Controls)
            {
                ctrl.Enabled = status;
            }
            if (!status)
            {
                this.btn_Cancel.Enabled = true;
            }
        }

        /// <summary>
        ///   DozeOff -> disable all controls (but the Exit button)
        /// </summary>
        /// 
        private void DozeOff()
        {
            EnableCommands(false);
        }

        /// <summary>
        ///   WakeUp -> enable all controls
        /// </summary>
        /// 
        public void WakeUp()
        {
            EnableCommands(true);
        }


        #endregion

        #region UI
        
        #region UI Helpers



        #endregion

        #region Button Clicks

        private void btn_Cancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            if (Environment.UserDomainName == "BECAMAIL")
            {
                using (frmPowerBIM_Help rqFrmHelp = new frmPowerBIM_Help())
                {
                    rqFrmHelp.ShowDialog();
                }
            }
            else
            {
                using (var bedarHelpFrm = new frmBedarPowerBIM_Help())
                {
                    bedarHelpFrm.ShowDialog();
                }
            }
        }
        #endregion

        #endregion

        #endregion

        private void rqSettingsSubmit_Click(object sender, EventArgs e)
        {
            // Volt drop calculation breaker rating percent
            if (radioButtonGPOCalcBreakerRating.Checked)
            {
                double breakerRatingPercent;
                if (double.TryParse(tb_VoltDropBreakerRatingPercent.Text, out breakerRatingPercent))
                {
                    _db.VoltDropCalculationBreakerRatingPercent = breakerRatingPercent / 100;
                }
                else
                {
                    MessageBox.Show("Please input a valid value in GPO percentage value.");
                    return;
                }
            }

            checkGPOCalc80perc = radioButtonGPOCalcBreakerRating.Checked;
            checkGPOActualLoad = radioButtonGPOCalcAssignedLoad.Checked;
            checkGPO1000plus100 = radioButtonGPOCalc1000plus100.Checked;

            checkClearingTimePower04 = radioButtonNonGPO04.Checked;
            checkClearingTimePower5 = radioButtonNonGPO5.Checked;

            checkClearingTimeLighting04 = radioButtonLightingClearning04.Checked;
            checkClearingTimeLighting5 = radioButtonLightingClearning5.Checked;

            _db.PowerVDCalculation = GetVoltDropCalculation(rb_PowerLinearDeprecating, rb_PowerLumpLoad);
            _db.OtherVDCalculation = GetVoltDropCalculation(rb_OtherLinearDeprecating, rb_OtherLumpLoad);
            _db.LightingVDCalculation = GetVoltDropCalculation(rb_LightingLinearDeprecating, rb_LightingLumpLoad);

            //Set GPO setting from revit
            if (checkGPOCalc80perc == true)
                _db.GPO_Calc_Integer = Convert.ToInt32(tb_VoltDropBreakerRatingPercent.Text);
            else if (checkGPOActualLoad == true)
                _db.GPO_Calc_Integer = -2;
            else if (checkGPO1000plus100 == true)
                _db.GPO_Calc_Integer = -1;

            //Set clearing time settings from revit
            if (checkClearingTimePower04 == true)
                _db.Clearing_Time_Power = 400;
            else
                _db.Clearing_Time_Power = 5000;

            //Set clearing time settings from revit
            if (checkClearingTimeLighting04 == true)
                _db.Clearing_Time_Lighting = 400;
            else
                _db.Clearing_Time_Lighting = 5000;

            //Set Safety Factors
            if (cbExtraLengthElem.Checked)
            {
                _db.Length_ExtraPerElement_enabled = true;
                _db.Length_ExtraPerElement_Lighting = double.Parse(tbTxtLenPerElemLighting.Text) * 1000;
                _db.Length_ExtraPerElement_Power = double.Parse(tbTxtLenPerElemPower.Text) * 1000;
                _db.Length_ExtraPerElement_Other = double.Parse(tbTxtLenPerElemOther.Text) * 1000;
            }
            else
            {
                _db.Length_ExtraPerElement_enabled = false;
                _db.Length_ExtraPerElement_Lighting = 0;
                _db.Length_ExtraPerElement_Power = 0;
                _db.Length_ExtraPerElement_Other = 0;
            }

            if (cbExtraLenPerCCT.Checked)
            {
                _db.Length_ExtraPerCCT_enabled = true;
                _db.Length_ExtraPerCCT_Lighting = double.Parse(tbTxtLenPerCCTLighting.Text) / 100;
                _db.Length_ExtraPerCCT_Power = double.Parse(tbTxtLenPerCCTPower.Text) / 100;
                _db.Length_ExtraPerCCT_Other = double.Parse(tbTxtLenPerCCTOther.Text) / 100;
            }
            else
            {
                _db.Length_ExtraPerCCT_enabled = false;
                _db.Length_ExtraPerCCT_Lighting = 0;
                _db.Length_ExtraPerCCT_Power = 0;
                _db.Length_ExtraPerCCT_Other = 0;
            }

            _db.Parameters_Changed = true;

            ModelessPowerBIM_DBSettingsFormHandler.DBData = _db;
            ModelessPowerBIM_StartFormHandler.ProjInfo = _db.Project_Info;
            MakeRequest(RequestId.Commit_DBSettings);

            this.Close();
        }

        private void frmPowerBIM_DBSettings_Load(object sender, EventArgs e)
        {
            Owner?.Hide();
        }

        private void frmPowerBIM_DBSettings_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (Owner is frmPowerBIM_Start ownerForm)
            {
                ownerForm.Show();
                ownerForm.ShowOrHideDB_SettingsColumn();
            }
        }

        private void tb_VoltDropBreakerRatingPercent_Validating(object sender, CancelEventArgs e)
        {
            int value;
            if (!int.TryParse(tb_VoltDropBreakerRatingPercent.Text, out value) || value < 1 || value > 100)
            {
                MessageBox.Show("Please enter an value between 1 and 100");
                e.Cancel = true;
            }
        }

        private void radioButtonGPOCalcBreakerRating_CheckedChanged(object sender, EventArgs e)
        {
            if (radioButtonGPOCalcBreakerRating.Checked)
                tb_VoltDropBreakerRatingPercent.Enabled = true;
            else
                tb_VoltDropBreakerRatingPercent.Enabled = false;
        }

        private void ValidateDoubleInput(System.Windows.Forms.TextBox tb, bool IsPerElement)
        {
            if (!IsDoubleRealNumber(tb.Text))
            {
                MessageBox.Show("Please input a number");
                tb.Text = "0";
                return;
            }

            double value = double.Parse(tb.Text);

            tb.Text = value.ToString();
        }

        private bool IsDoubleRealNumber(string valueToTest)
        {
            if (double.TryParse(valueToTest, out double d) && !Double.IsNaN(d) && !Double.IsInfinity(d))
            {
                return true;
            }

            return false;
        }

        private void tbTxtLenPerElemLighting_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerElemLighting, true);
        }

        private void tbTxtLenPerElemLighting_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerElemLighting, true);
        }

        private void tbTxtLenPerElemPower_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerElemPower, true);
        }

        private void tbTxtLenPerElemPower_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerElemPower, true);
        }

        private void tbTxtLenPerElemOther_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerElemOther, true);
        }

        private void tbTxtLenPerElemOther_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerElemOther, true);
        }

        private void tbTxtLenPerCCTLighting_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerCCTLighting, false);
        }

        private void tbTxtLenPerCCTLighting_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerCCTLighting, false);
        }

        private void tbTxtLenPerCCTPower_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerCCTPower, false);
        }

        private void tbTxtLenPerCCTPower_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerCCTPower, false);
        }

        private void tbTxtLenPerCCTOther_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerCCTOther, false);
        }

        private void tbTxtLenPerCCTOther_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerCCTOther, false);
        }
    }

    #region Classes to be Created

    #region Modeless Form Handler 

    public class ModelessPowerBIM_DBSettingsFormHandler
    {
        #region Fields

        public static frmPowerBIM_DBSettings FrmModelessDBSettings;
        public static PowerBIM_DBData DBData;
        #endregion

        #region Properties

        #endregion

        #region Methods

        public static void Commit_AdvancedSettings()
        {
            DBData.Commit_DBSettings();
        }

        /// <summary>
        ///   Waking up the dialog from its waiting state.
        /// </summary>
        /// 
        public static void WakeFormUp()
        {
            if (FrmModelessDBSettings != null)
            {
                FrmModelessDBSettings.WakeUp();
            }
        }

        /// <summary>
        /// Must be called in OnShutdown(UIControlledApplication a) Event of the App command.
        /// </summary>
        public static void OnRevitShutDown()
        {
            if (FrmModelessDBSettings != null)
            {
                FrmModelessDBSettings.Close();
            }
        }

        #endregion

    }

    #endregion

    #endregion

}