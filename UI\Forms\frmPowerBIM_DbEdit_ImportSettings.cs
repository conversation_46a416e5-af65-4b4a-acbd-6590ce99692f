﻿using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Autodesk.Revit.UI;
using MEP.PowerBIM_1._5.UI.UiData;
using Common.Utilities;
using MEP.PowerBIM_5.CoreLogic;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmPowerBIM_DbEdit_ImportSettings : BecaBaseForm
    {
        #region Fields
        PowerBIM_ProjectInfo _pi;
        private IList<PowerBIM_DBData> _dBD = null;

        private List<dgvDBImportData> ExistingDBdata = new List<dgvDBImportData>();
        private int errCount = 0;
        private int TotalEntries = 0;
        private int Match = 0;
        public string[,] strPowerCADdata;

        string _noMatch = "no match";

        Dictionary<string, int> _csvRowIndex = new Dictionary<string, int>();
        public Dictionary<string, int> MatchRowIndex = new Dictionary<string, int>();

        public string strInitialFolder;
        #endregion

        #region Constructor
        public frmPowerBIM_DbEdit_ImportSettings(List<dgvDBImportData> data, PowerBIM_ProjectInfo pi, IList<PowerBIM_DBData> dBD)
        {
            ExistingDBdata = data;
            InitializeComponent();

            _pi = pi;
            _dBD = dBD;

            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("RevitDB");
            dataTable.Columns.Add("Match");

            foreach (var item in ExistingDBdata)
                dataTable.Rows.Add(item.DB_Name, "");

            dgbDBMatchList.DataSource = dataTable;

            // We don't really need the left empty selection column.
            dgbDBMatchList.RowHeadersVisible = false;

            // Turn on manual control for header formatting
            dgbDBMatchList.EnableHeadersVisualStyles = false;

            // Defining the formatting.
            dgbDBMatchList.DefaultCellStyle.Font = new Font("Arial Narrow", 9);

            // make default selections
            rb_NoneDuplicate.Checked = true;
            rb_ExactMatch.Checked = true;
        }
        #endregion

        #region Methods
        private string ComputeCableName(int matchRow)
        {

            string strFeederCableArangement = "";
            if (strPowerCADdata[matchRow, 12].Contains("4C&E"))
            {
                strFeederCableArangement = "1x4C";
            }
            else if (strPowerCADdata[matchRow, 12].Contains("S.D.I"))
            {
                strFeederCableArangement = "4x1C";
            }
            else if (strPowerCADdata[matchRow, 12].Contains("2C&E"))
            {
                strFeederCableArangement = "1x2C";
            }
            else if (strPowerCADdata[matchRow, 12].Contains("Busway"))
            {
                strFeederCableArangement = "BUSBAR";
            }
            else
            {
                strFeederCableArangement = "Error";
            }

            string strFeederCableActiveSize = strPowerCADdata[matchRow, 14];
            string strFeederCableActiveMaterial = strPowerCADdata[matchRow, 18];

            // Primary cable insulation
            // TB updated 2024.4 to accept more cable defintions
            string strFeederCableInsulation = "";
            if (strPowerCADdata[matchRow, 15].Contains("XLPE") || (strPowerCADdata[matchRow, 15].Contains("90")))
            {
                strFeederCableInsulation = "XLPE";
            }
            else if (strPowerCADdata[matchRow, 15].Contains("PVC"))
            {
                strFeederCableInsulation = "PVC";
            }
            else if (strPowerCADdata[matchRow, 15].Contains("110"))
            {
                strFeederCableInsulation = "FR";
            }
            else
            {
                strFeederCableInsulation = "Error";
            }

            // Secondary cable insulation
            // TB updated 2024.4 to accept more cable defintions
            string strFeederCableActiveSheath = "";
            string strFeederCableEarthSheath = "";
            if (strPowerCADdata[matchRow, 16].Contains("No Sheath") == true)
            {
                strFeederCableActiveSheath = "";
            }
            else if (strPowerCADdata[matchRow, 16].Contains("XLPE") || (strPowerCADdata[matchRow, 16].Contains("90")))
            {
                strFeederCableActiveSheath = "/XLPE";
                strFeederCableEarthSheath = "XLPE";
            }
            else if (strPowerCADdata[matchRow, 16].Contains("PVC") || (strPowerCADdata[matchRow, 16].Contains("70")))
            {
                strFeederCableActiveSheath = "/PVC";
                strFeederCableEarthSheath = "PVC";
            }
            else if (strPowerCADdata[matchRow, 16].Contains("FR") || (strPowerCADdata[matchRow, 16].Contains("110")))
            {
                strFeederCableActiveSheath = "/FR";
                strFeederCableEarthSheath = "FR";
            }
            else
            {
                strFeederCableActiveSheath = "/Error";
                strFeederCableEarthSheath = "Error";
            }

            string strFeederCableSheild = "";
            if (strPowerCADdata[matchRow, 17].Contains("No Shield") == true)
            {
                strFeederCableSheild = "";
            }
            else
            {
                strFeederCableSheild = "/" + strPowerCADdata[matchRow, 17];
            }

            string strFeederCableEarthMaterial = strPowerCADdata[matchRow, 23];
            string strFeederCableEarthSize = strPowerCADdata[matchRow, 22];

            //Concat to get cable name
            string strFeederCable = "";
            if ((strFeederCableArangement == "Error") || (strFeederCableInsulation == "Error") || (strFeederCableActiveSheath == "Error"))
            {
                strFeederCable = "Error - Please enter manually";
                errCount++;
            }
            else
            {
                strFeederCable = strFeederCableArangement + " " + strFeederCableActiveSize + "mm² " + strFeederCableActiveMaterial
                    + " " + strFeederCableInsulation + strFeederCableActiveSheath + strFeederCableSheild + " + " + strFeederCableEarthSize
                    + "mm² " + strFeederCableEarthMaterial + " " + strFeederCableEarthSheath + " E";
            }
            return strFeederCable;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="intGridRow"></param>
        /// <param name="CSVRow"></param>
        /// <returns>failed values</returns>
        public dgvDBImportDataStatus WriteBackToDGV(int intGridRow, int CSVRow)
        {
            double strValue = 0;
            dgvDBImportDataStatus result = new dgvDBImportDataStatus(intGridRow);

            ExistingDBdata.ElementAt(intGridRow).Feeder_Cable = ComputeCableName(CSVRow);



            try
            {
                // DBVD
                if (double.TryParse(strPowerCADdata[CSVRow, 6], out strValue))
                {
                    ExistingDBdata.ElementAt(intGridRow).DBVD = strValue * 0.01;
                    result.DBVD = true;
                }
                else
                {
                    ExistingDBdata.ElementAt(intGridRow).DBVD = 0;
                    result.DBVD = false;
                }


                // PSCC
                if (double.TryParse(strPowerCADdata[CSVRow, 7], out strValue))
                {
                    ExistingDBdata.ElementAt(intGridRow).PSCC = strValue;
                    result.PSCC = true;
                }
                else
                {
                    ExistingDBdata.ElementAt(intGridRow).PSCC = 0;
                    result.PSCC = false;
                }

                string[] strEFLiZ = strPowerCADdata[CSVRow, 11].Split('+');
                string strEFLiR = strEFLiZ[0];
                string strEFLiX = strEFLiZ[1].Substring(2, (strEFLiZ[1].Count() - 2));

                // EFLI
                if (double.TryParse(strEFLiR, out strValue))
                {
                    ExistingDBdata.ElementAt(intGridRow).EFLI_R = strValue;
                    result.EFLI_R = true;
                }
                else
                {
                    ExistingDBdata.ElementAt(intGridRow).EFLI_R = 0;
                    result.EFLI_R = false;
                }

                if (double.TryParse(strEFLiX, out strValue))
                {
                    ExistingDBdata.ElementAt(intGridRow).EFLI_X = strValue;
                    result.EFLI_X = true;
                }
                else
                {
                    ExistingDBdata.ElementAt(intGridRow).EFLI_X = 0;
                    result.EFLI_X = false;
                }

                // Upstream device rating
                if (double.TryParse(strPowerCADdata[CSVRow, 32], out strValue))
                {
                    ExistingDBdata.ElementAt(intGridRow).Upstream_Device_Rating = strValue;
                    result.Upstream_Device_Rating = true;
                }
                else
                {
                    ExistingDBdata.ElementAt(intGridRow).Upstream_Device_Rating = 0;
                    result.Upstream_Device_Rating = false;
                }

                // Main Switch rating
                if (double.TryParse(strPowerCADdata[CSVRow, 40], out strValue))
                {
                    ExistingDBdata.ElementAt(intGridRow).Main_Switch_Rating = strValue;
                    result.Main_Switch_Rating = true;
                }
                else
                {
                    ExistingDBdata.ElementAt(intGridRow).Main_Switch_Rating = 0;
                    result.Main_Switch_Rating = false;
                }
            }
            catch (Exception)
            {
                string message ="Unable to read csv file. \n \n" + "Check that 'Impedance Zsc (Ohms)' is in column K of the spreadsheet";
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Import Error!", message);

                this.DialogResult = DialogResult.Cancel;
                return result;
            }

            return result;
        }

        private void ClearMatch()
        {
            foreach (DataGridViewRow row in dgbDBMatchList.Rows)
                row.Cells[1].Value = null;
        }

        private void CSVRbCheckedSet(bool b)
        {
            rb_ExactMatch.Checked = b;
            rb_AddSuffix.Checked = b;

        }

        private void DuplicateDBCheckedSet(bool b)
        {
            rb_NoneDuplicate.Checked = b;
            rb_PL.Checked = b;
            rb_PLM.Checked = b;
            rb_AB.Checked = b;
            rb_12.Checked = b;
        }

        private void ApplyDuplicateDelimiter(RadioButton rb, string str1, string str2, string str3)
        {
            if (rb.Checked && lb_dbCSV.Items.Count > 0)
            {
                if (rb_RemoveSuffixMatch.Checked)
                {
                    ApplyCSVAndDBDelimiter(str1, str2, str3);
                }
                else
                {
                    foreach (DataGridViewRow row in dgbDBMatchList.Rows)
                    {
                        var str = row.Cells[0].Value.ToString();
                        if (str.Substring(str.Length - 2) == tbDelimiter.Text + str1 ||
                            str.Substring(str.Length - 2) == tbDelimiter.Text + str2 ||
                            str.Substring(str.Length - 2) == tbDelimiter.Text + str3)
                        {
                            var index = lb_dbCSV.FindStringExact(str.Substring(0, str.Length - 2));
                            if (index != -1)
                                row.Cells[1].Value = lb_dbCSV.Items[index];
                            else
                                row.Cells[1].Value = _noMatch;

                        }
                        else
                            row.Cells[1].Value = _noMatch;
                    }
                }

            }
            else
                ClearMatch();
        }

        private void ApplyCSVAndDBDelimiter(string str1, string str2, string str3)
        {
            var removedCSVSuffix = new List<string>();
            foreach (var item in lb_dbCSV.Items)
            {
                if (item.ToString().Contains(tb_remove.Text))
                    removedCSVSuffix.Add(item.ToString().Substring(0, item.ToString().IndexOf(tb_remove.Text)));
                else
                    removedCSVSuffix.Add(item.ToString());
            }

            var dbDelimiter = new List<string>();
            foreach (DataGridViewRow row in dgbDBMatchList.Rows)
            {
                var str = row.Cells[0].Value.ToString();
                if (str.Substring(str.Length - 2) == tbDelimiter.Text + str1 ||
                    str.Substring(str.Length - 2) == tbDelimiter.Text + str2 ||
                    str.Substring(str.Length - 2) == tbDelimiter.Text + str3)
                {
                    dbDelimiter.Add(str.Substring(0, str.Length - 2));
                }
                else
                    dbDelimiter.Add(str);
            }

            int index = 0;
            foreach (DataGridViewRow row in dgbDBMatchList.Rows)
            {
                var removedCSVSuffixIndex = removedCSVSuffix.IndexOf(dbDelimiter[index]);
                if (removedCSVSuffixIndex != -1)
                {
                    row.Cells[1].Value = removedCSVSuffix[removedCSVSuffixIndex];
                }
                else
                    row.Cells[1].Value = _noMatch;
                index++;
            }
        }
        #endregion

        #region Button Clicks
        private void btnImport_Click(object sender, EventArgs e)
        {
            // get ls matches?
            foreach (DataGridViewRow row in dgbDBMatchList.Rows)
            {
                if (row.Cells[1].Value.ToString() != _noMatch && _csvRowIndex.TryGetValue(row.Cells[1].Value.ToString(), out int matchRow))
                {
                    ComputeCableName(matchRow);
                    MatchRowIndex.Add(row.Cells[0].Value.ToString(), _csvRowIndex[row.Cells[1].Value.ToString()]);
                    Match++;
                }
            }
            string message = "Import complete: \n" + Match + " DBs were able to be matched. \n" + (dgbDBMatchList.Rows.Count - Match) + " Unable to be matched. \n" + errCount + " Feed cable names to be manually entered";
            UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Complete!", message);

            this.DialogResult = DialogResult.OK;

        }

        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog rqOfd = new OpenFileDialog())
            {

                rqOfd.Filter = "CSV|*.csv";
                rqOfd.InitialDirectory = strInitialFolder;


                if (rqOfd.ShowDialog() == DialogResult.OK)
                {
                    string strPowerCADcsvFile = rqOfd.FileName;
                    rqTxtCSVsel.Text = strPowerCADcsvFile;

                    //Read PowerCAD Data from CSV
                    strPowerCADdata = new string[PowerBIM_Constants.file_PowerCADRowsMax, PowerBIM_Constants.file_PowerCADColumnsMax];
                    CSVUtility.WriteTo2DArray(ref strPowerCADdata, strPowerCADcsvFile, PowerBIM_Constants.file_PowerCADRowStart - 1, PowerBIM_Constants.file_PowerCADColumnsMax);
                    //Finish Reading PowerCAD Data

                    for (int i = 0; i < PowerBIM_Constants.file_PowerCADRowsMax; i++)
                    {
                        if (strPowerCADdata[i, 0] != null&& !string.IsNullOrEmpty(strPowerCADdata[i, 0]))
                        {
                            if (!_csvRowIndex.ContainsKey(strPowerCADdata[i, 0]))
                            {
                                lb_dbCSV.Items.Add(strPowerCADdata[i, 0]);
                                _csvRowIndex.Add(strPowerCADdata[i, 0], i);
                            }
                        }

                    }
                }
            }
        }

        private void btnClose_Click_1(object sender, EventArgs e)
        {
            UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Import Cancelled", "Import Cancelled! \n\nNothing was imported");
        }
        #endregion

        #region UI Events
        private void rb_ExactMatch_CheckedChanged(object sender, EventArgs e)
        {
            DuplicateDBCheckedSet(false);
            if (rb_ExactMatch.Checked && lb_dbCSV.Items.Count > 0)
            {
                foreach (DataGridViewRow row in dgbDBMatchList.Rows)
                {
                    var index = lb_dbCSV.FindStringExact(row.Cells[0].Value.ToString());
                    if (index != -1)
                        row.Cells[1].Value = lb_dbCSV.Items[index];
                    else
                        row.Cells[1].Value = _noMatch;
                }
            }
            else
                ClearMatch();

        }

        private void rb_AddSuffix_CheckedChanged(object sender, EventArgs e)
        {
            DuplicateDBCheckedSet(false);
            var added = new List<string>();
            if (rb_AddSuffix.Checked && lb_dbCSV.Items.Count > 0)
            {
                foreach (var item in lb_dbCSV.Items)
                    added.Add(item + tbAdd.Text);

                foreach (DataGridViewRow row in dgbDBMatchList.Rows)
                {
                    var index = added.IndexOf(row.Cells[0].Value.ToString());
                    if (index != -1)
                        row.Cells[1].Value = lb_dbCSV.Items[index];
                    else
                        row.Cells[1].Value = _noMatch;
                }
            }
            else
                ClearMatch();
        }

        private void rb_removeSuffixMatch_CheckedChanged(object sender, EventArgs e)
        {
            var removed = new List<string>();
            if (rb_RemoveSuffixMatch.Checked && lb_dbCSV.Items.Count > 0)
            {
                if (rb_PL.Checked || rb_PLM.Checked || rb_AB.Checked || rb_12.Checked)
                {
                    if (rb_PL.Checked)
                        ApplyCSVAndDBDelimiter("P", "L", "");
                    if (rb_PLM.Checked)
                        ApplyCSVAndDBDelimiter("P", "L", "M");
                    if (rb_AB.Checked)
                        ApplyCSVAndDBDelimiter("A", "B", "");
                    if (rb_12.Checked)
                        ApplyCSVAndDBDelimiter("1", "2", "");
                }
                else
                {
                    foreach (var item in lb_dbCSV.Items)
                    {
                        if (item.ToString().Contains(tb_remove.Text))
                            removed.Add(item.ToString().Substring(0, item.ToString().IndexOf(tb_remove.Text)));
                        else
                            removed.Add(item.ToString());
                    }

                    foreach (DataGridViewRow row in dgbDBMatchList.Rows)
                    {
                        var index = removed.IndexOf(row.Cells[0].Value.ToString());
                        if (index != -1)
                            row.Cells[1].Value = lb_dbCSV.Items[index];
                        else
                            row.Cells[1].Value = _noMatch;
                    }
                }
            }
            else
                ClearMatch();

        }

        private void tb_remove_TextChanged(object sender, EventArgs e)
        {
            CSVRbCheckedSet(false);
            rb_RemoveSuffixMatch.Checked = false;

        }

        private void tbAdd_TextChanged(object sender, EventArgs e)
        {
            CSVRbCheckedSet(false);
        }

        private void rb_NoneDuplicate_CheckedChanged(object sender, EventArgs e)
        {
            CSVRbCheckedSet(false);
            ClearMatch();
        }

        private void rb_PL_CheckedChanged(object sender, EventArgs e)
        {
            CSVRbCheckedSet(false);
            ApplyDuplicateDelimiter(rb_PL, "P", "L", "");
        }

        private void rb_PLM_CheckedChanged(object sender, EventArgs e)
        {
            CSVRbCheckedSet(false);
            ApplyDuplicateDelimiter(rb_PLM, "P", "L", "M");
        }

        private void rb_AB_CheckedChanged(object sender, EventArgs e)
        {
            CSVRbCheckedSet(false);
            ApplyDuplicateDelimiter(rb_AB, "A", "B", "");
        }

        private void rb_12_CheckedChanged(object sender, EventArgs e)
        {
            CSVRbCheckedSet(false);
            ApplyDuplicateDelimiter(rb_12, "1", "2", "");
        }

        private void tbDelimiter_TextChanged(object sender, EventArgs e)
        {
            DuplicateDBCheckedSet(false);
        }

        private void tb_remove_Leave(object sender, EventArgs e)
        {
            if (String.IsNullOrEmpty(tb_remove.Text))
                tb_remove.Text = "/";
        }

        private void tbDelimiter_Leave(object sender, EventArgs e)
        {
            if (String.IsNullOrEmpty(tbDelimiter.Text))
                tbDelimiter.Text = "/";
        }


        #endregion

        private void lb_dbCSV_DoubleClick(object sender, EventArgs e)
        {
            var selectedCells = dgbDBMatchList.SelectedCells;
            foreach (DataGridViewCell cell in dgbDBMatchList.SelectedCells)
            {
                if (cell.ColumnIndex == dgbDBMatchList.Columns["Match"].Index)
                {
                    cell.Value = lb_dbCSV.SelectedItem.ToString();
                }
            }
        }
    }
}