﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB.Electrical;
using BecaTransactionsNamesManager;
using BecaRevitUtilities;
using BecaRevitUtilities.Collectors;
using BecaRevitCommonAlgorithms;
using GEN.Easy3DView.CoreLogic;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitElementsCreators;
using Autodesk.Revit.UI.Events;
using BecaActivityLogger.UI.Forms;
using View = Autodesk.Revit.DB.View;
using BecaRevitUtilities.RevitCommandsCaller;
using System.Windows.Forms;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class PowerBIM_DBRoute
    {

        public PowerBIM_ProjectInfo projInfo { get; set; }
        public PowerBIM_DBData DBData { get; set; }


        public double Length_Total { get; set; }


        public string Circuit_Path_Mode { get; set; }

        public PowerBIM_DBRoute(PowerBIM_ProjectInfo pi, PowerBIM_DBData CCT)
        {
            projInfo = pi;
            DBData = CCT;
        }




        public void CCT_CalculateCircuitLength(/*out List<string> BadCircuits*/)
        {
            //BadCircuits = new List<string>();
            var eleSystem = DBData.DB_ElectricalSystem;
            if (eleSystem == null)
            {
                return;
            }

            double dblLen_Total = 0;


            IList<XYZ> ListOfPathNodes = eleSystem.GetCircuitPath();   //list of revit path node coordinates
            ElementSet CircuitElems = eleSystem.Elements;
            XYZ firstElementXYZ = null;

            foreach (XYZ point in ListOfPathNodes)// Go through to find first element on the circuit path
            {
                foreach (Element rqCTTelem in CircuitElems)
                {
                    // Get box around the element
                    BoundingBoxXYZ elemBounds = rqCTTelem.get_BoundingBox(null);

                    //find the first element that is on the revit path         
                    if (BoundingBoxXYZContains(elemBounds, point))
                    {
                        firstElementXYZ = point;
                        break;
                    }
                }
                if (firstElementXYZ != null)//if first element has been located the loop can end
                {
                    break;
                }
            }

            //
            // --------- no elements on path so length to first is length to final ---------
            //

            if (firstElementXYZ == null)
            {
                try
                {
                    //BadCircuits.Add(Circuit.CCT_Number); // Add wrong path as bad circuit
                    dblLen_Total = RevitUnitConvertor.InternalToMm(eleSystem.Length) + projInfo.GUI_Calc_Lengths_Extra_Termination; //uses the internal revit variable for length to be certain

                    if (dblLen_Total < PowerBIM_Constants.Length_IsInternalThreshold)//if length is less than 5m length is negligible so set to zero
                    {
                        dblLen_Total = 0;
                    }
                    else
                    {
                        //Add extra length for each circuit element
                        dblLen_Total = dblLen_Total + (CircuitElems.Size);
                    }

                    //Write back to global parameters
                    Length_Total = dblLen_Total;
                }
                catch (Exception)
                {
                    // Some case eleSystem.Length is not computed, so set length total to 0
                    Length_Total = 0;
                }
                

            }

            //
            // ---------- NORMAL CASE ---------------
            //
            else
            {
                //compute length to first by going along path and recording distances
                XYZ prevPoint = DBData.DB_Location;//first node is DB



                //the first plus one extra length 
                //Add safty factor and termination length
                dblLen_Total = RevitUnitConvertor.InternalToMm(eleSystem.Length) + projInfo.GUI_Calc_Lengths_Extra_Termination; //uses the internal revit variable for length to be certain

                //** 1.44 If the total length is too short <5m, set both lengths to zero
                if (dblLen_Total < PowerBIM_Constants.Length_IsInternalThreshold)
                {
                    dblLen_Total = 0;
                }
                else
                {
                    //Add extra length for each circuit element
                    dblLen_Total = dblLen_Total + (CircuitElems.Size);
                }

                // Write back to global parameters
                Length_Total = dblLen_Total;

            }
        }


        public bool BoundingBoxXYZContains(BoundingBoxXYZ bb, XYZ p)
        {
            //check if point is greater than the minimum extent of the box and less than the maximum extent 
            double tolerance = RevitUnitConvertor.MmToInternal(projInfo.NodeCircuitPathTolerance);

            if (((bb.Min.X - tolerance) < p.X) && ((bb.Min.Y - tolerance) < p.Y))//is point greater than min bounds of box
            {
                if (((bb.Max.X + tolerance) > p.X) && ((bb.Max.Y + tolerance) > p.Y))//is point less than max bounds of box
                {
                    return true;
                }
            }
            return false;

        }

        public void UpdateRevitPathMode(bool pathModeChangedFromEditCircuit, out string lockedUserName)
        {
            lockedUserName = string.Empty;
            var eleSystem = DBData.DB_ElectricalSystem;

            //uses the Circuit_Path_mode string to update the path in revit
            // Check element's owner
            var owner = WorksharingUtils.GetWorksharingTooltipInfo(projInfo.Document, eleSystem.Id).Owner;
            if ((owner == projInfo.Document.Application.Username || owner == "") && pathModeChangedFromEditCircuit)
            {
                //uses the Circuit_Path_mode string to update the path in revit
                using (var Trans_CCT = new SubTransaction(projInfo.Document))
                {
                    Trans_CCT.Start();
                    eleSystem.CircuitPathMode = (Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode)Enum.Parse(typeof(Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode), Circuit_Path_Mode, true); //converts string into revit's enum to set revit path mode
                    Trans_CCT.Commit();
                }
                projInfo.Document.Regenerate();
            }
            if (owner != projInfo.Document.Application.Username && owner != "")
            {
                lockedUserName = owner;
            }

        }

        public bool OpenPathCustomisingView()
        {
            var eleSystem = DBData.DB_ElectricalSystem;
            if (eleSystem == null)
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "Failed to create 3D View: DB has no electrical system.");
                return false;
            }
            PowerBIM_3DViewLogic ThreeDLogic = new PowerBIM_3DViewLogic(projInfo); //initialize logic for creating 3D view

            double buffer = RevitUnitConvertor.MmToInternal(4000);

            IList<XYZ> ListOfPathNodes = eleSystem.GetCircuitPath();   //list of revit path node coordinates

            Autodesk.Revit.DB.ElementSet CircuitElems = eleSystem.Elements;
            List<Autodesk.Revit.DB.Element> elements = (from Autodesk.Revit.DB.Element e in CircuitElems
                                                        select e).ToList(); //convert revit element set into list of elements
            BoundingBoxCalculator bound = BoundingBoxCalculator.Create(projInfo.Document, ListOfPathNodes, projInfo.TaskLogger);

            if (bound != null)
            {
                //find template by name using LINQ
                View viewTemplate = (from v in new FilteredElementCollector(projInfo.Document).OfClass(typeof(View)).Cast<View>()
                                     where v.IsTemplate == true && v.Name.Equals("WR_77_3D_POWERBIM")
                                     select v).SingleOrDefault();

                //if view exists edit it to suit circuit. if it does not create a new one from scratch
                if (ThreeDLogic._ViewExists)
                {
                    PowerBIM_3DViewLogic.RunLogic(projInfo.UIDocument, ThreeDLogic._PB3DView as View3D, buffer, bound, false, projInfo.TaskLogger);//this modifies an already existing view 
                    return true;
                }
                else if (viewTemplate == null)//if view template does not exist view cant be created
                {
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "PowerBIM view could not be created because PowerBIM view template does not exist \n \n Please create a 3D view named WR_77_3D_POWERBIM and try again");
                    return false;
                }
                else
                {
                    PowerBIM_3DViewLogic.RunLogic(projInfo.UIDocument, ThreeDLogic._UserViewName, viewTemplate as View, buffer, bound, false, projInfo.TaskLogger); //this creates a new view 
                    return true;
                }
            }
            else
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "Failed to create 3D View: bounding box is null.");
                return false;
            }

        }

    }
}


