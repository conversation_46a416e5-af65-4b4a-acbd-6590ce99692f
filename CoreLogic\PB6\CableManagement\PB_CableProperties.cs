﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MEP.PowerBIM_6.CableManagement;
using MEP.PowerBIM_6.Database;

namespace MEP.PowerBIM_6.CableManagement
{
    internal class PB_CableProperties
    {
        public string countryStandard { get; private set; }
        public bool isCustom { get; private set; }
        private PB_DatabaseClient db;
        public DataTable currentDf { get; private set; }
        public DataTable reactanceDf { get; private set; }
        public DataTable resistanceDf { get; private set; }
        public DataTable breakerDf { get; private set; }
        public DataTable shortCircuitWithstandDf { get; private set; }
        public DataTable cablesDf { get; private set; }

        private Dictionary<string, PB_Cable> cachedCables;

 

        public PB_CableProperties(string countryStandard, bool isCustom = false)
        {
            this.countryStandard = countryStandard;
            this.isCustom = isCustom;

            db = new PB_DatabaseClient();
            db.Connect();

            currentDf = db.GetAllRowsFromTableViaStandard("PB_Current", $"%{this.countryStandard}%");
            reactanceDf = db.GetAllRowsFromTableViaStandard("PB_Reactance", $"%{this.countryStandard}%");
            resistanceDf = db.GetAllRowsFromTableViaStandard("PB_Resistance", $"%{this.countryStandard}%");
            breakerDf = db.GetAllRowsFromTable("PB_Breaker");
            shortCircuitWithstandDf = db.GetAllRowsFromTable("PB_Short_Circuit_Withstand");


            if (!this.isCustom)
            {
                cablesDf = db.GetAllRowsFromTableViaStandard("PB_Default_Cable", $"%{this.countryStandard}%");
                //cachedCables = PopulateCables();
            }
            else
            {
                Console.WriteLine("Custom Cables not supported yet!");
            }

            db.Close();
        }

        public Dictionary<string, PB_Cable> PopulateCables()
        {
            Dictionary<string, PB_Cable> cables = new Dictionary<string, PB_Cable>(cablesDf.Rows.Count);
            Console.WriteLine("Populating cables");

            Parallel.ForEach(cablesDf.Rows.Cast<DataRow>(), row =>
            {
                PB_Cable cable = new PB_Cable(countryStandard, row, this);

                lock (cables) // Ensure thread-safe access to the dictionary
                {
                    if (!cables.ContainsKey(cable.name))
                    {
                        cables.Add(cable.name, cable);
                    }
                }
            });

            return cables;
        }


        public Dictionary<string, PB_Cable> GetCables()
        {
            if (cachedCables == null)
            {
                cachedCables = PopulateCables();
            }
            return cachedCables;
        }
    }
}
