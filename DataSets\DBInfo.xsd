﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DBInfo" targetNamespace="http://tempuri.org/DBInfo.xsd" xmlns:mstns="http://tempuri.org/DBInfo.xsd" xmlns="http://tempuri.org/DBInfo.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DBInfo" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="DBInfo" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DBInfo">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DBInformation" msprop:Generator_RowClassName="DBInformationRow" msprop:Generator_RowEvHandlerName="DBInformationRowChangeEventHandler" msprop:Generator_RowDeletedName="DBInformationRowDeleted" msprop:Generator_RowDeletingName="DBInformationRowDeleting" msprop:Generator_RowEvArgName="DBInformationRowChangeEvent" msprop:Generator_TablePropName="DBInformation" msprop:Generator_RowChangedName="DBInformationRowChanged" msprop:Generator_UserTableName="DBInformation" msprop:Generator_RowChangingName="DBInformationRowChanging" msprop:Generator_TableClassName="DBInformationDataTable" msprop:Generator_TableVarName="tableDBInformation">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="DB_Name" msprop:Generator_UserColumnName="DB_Name" msprop:Generator_ColumnPropNameInTable="DB_NameColumn" msprop:Generator_ColumnPropNameInRow="DB_Name" msprop:Generator_ColumnVarNameInTable="columnDB_Name" type="xs:string" minOccurs="0" />
              <xs:element name="Main_Switch_Rating" msprop:Generator_UserColumnName="Main_Switch_Rating" msprop:Generator_ColumnPropNameInTable="Main_Switch_RatingColumn" msprop:Generator_ColumnPropNameInRow="Main_Switch_Rating" msprop:Generator_ColumnVarNameInTable="columnMain_Switch_Rating" type="xs:double" minOccurs="0" />
              <xs:element name="Feeder_Cable" msprop:Generator_UserColumnName="Feeder_Cable" msprop:Generator_ColumnPropNameInTable="Feeder_CableColumn" msprop:Generator_ColumnPropNameInRow="Feeder_Cable" msprop:Generator_ColumnVarNameInTable="columnFeeder_Cable" type="xs:string" minOccurs="0" />
              <xs:element name="Location" msprop:Generator_UserColumnName="Location" msprop:Generator_ColumnPropNameInTable="LocationColumn" msprop:Generator_ColumnPropNameInRow="Location" msprop:Generator_ColumnVarNameInTable="columnLocation" type="xs:string" minOccurs="0" />
              <xs:element name="Seismic_Category" msprop:Generator_UserColumnName="Seismic_Category" msprop:Generator_ColumnPropNameInTable="Seismic_CategoryColumn" msprop:Generator_ColumnPropNameInRow="Seismic_Category" msprop:Generator_ColumnVarNameInTable="columnSeismic_Category" type="xs:string" minOccurs="0" />
              <xs:element name="Form_Rating" msprop:Generator_UserColumnName="Form_Rating" msprop:Generator_ColumnPropNameInTable="Form_RatingColumn" msprop:Generator_ColumnPropNameInRow="Form_Rating" msprop:Generator_ColumnVarNameInTable="columnForm_Rating" type="xs:string" minOccurs="0" />
              <xs:element name="Bus_Fault_Level" msprop:Generator_UserColumnName="Bus_Fault_Level" msprop:Generator_ColumnPropNameInTable="Bus_Fault_LevelColumn" msprop:Generator_ColumnPropNameInRow="Bus_Fault_Level" msprop:Generator_ColumnVarNameInTable="columnBus_Fault_Level" type="xs:string" minOccurs="0" />
              <xs:element name="Surge_Protection" msprop:Generator_UserColumnName="Surge_Protection" msprop:Generator_ColumnPropNameInTable="Surge_ProtectionColumn" msprop:Generator_ColumnPropNameInRow="Surge_Protection" msprop:Generator_ColumnVarNameInTable="columnSurge_Protection" type="xs:string" minOccurs="0" />
              <xs:element name="Metering" msprop:Generator_UserColumnName="Metering" msprop:Generator_ColumnPropNameInTable="MeteringColumn" msprop:Generator_ColumnPropNameInRow="Metering" msprop:Generator_ColumnVarNameInTable="columnMetering" type="xs:string" minOccurs="0" />
              <xs:element name="Upstream_Device_Rating" msprop:Generator_UserColumnName="Upstream_Device_Rating" msprop:Generator_ColumnPropNameInTable="Upstream_Device_RatingColumn" msprop:Generator_ColumnPropNameInRow="Upstream_Device_Rating" msprop:Generator_ColumnVarNameInTable="columnUpstream_Device_Rating" type="xs:double" minOccurs="0" />
              <xs:element name="EFLI_R" msprop:Generator_UserColumnName="EFLI_R" msprop:Generator_ColumnPropNameInTable="EFLI_RColumn" msprop:Generator_ColumnPropNameInRow="EFLI_R" msprop:Generator_ColumnVarNameInTable="columnEFLI_R" type="xs:double" minOccurs="0" />
              <xs:element name="EFLI_X" msprop:Generator_UserColumnName="EFLI_X" msprop:Generator_ColumnPropNameInTable="EFLI_XColumn" msprop:Generator_ColumnPropNameInRow="EFLI_X" msprop:Generator_ColumnVarNameInTable="columnEFLI_X" type="xs:double" minOccurs="0" />
              <xs:element name="DBVD" msprop:Generator_UserColumnName="DBVD" msprop:Generator_ColumnPropNameInTable="DBVDColumn" msprop:Generator_ColumnPropNameInRow="DBVD" msprop:Generator_ColumnVarNameInTable="columnDBVD" type="xs:double" minOccurs="0" />
              <xs:element name="PSCC" msprop:Generator_UserColumnName="PSCC" msprop:Generator_ColumnPropNameInTable="PSCCColumn" msprop:Generator_ColumnPropNameInRow="PSCC" msprop:Generator_ColumnVarNameInTable="columnPSCC" type="xs:double" minOccurs="0" />
              <xs:element name="Device_Fault_Rating" msprop:Generator_UserColumnName="Device_Fault_Rating" msprop:Generator_ColumnPropNameInTable="Device_Fault_RatingColumn" msprop:Generator_ColumnPropNameInRow="Device_Fault_Rating" msprop:Generator_ColumnVarNameInTable="columnDevice_Fault_Rating" type="xs:string" minOccurs="0" />
              <xs:element name="Final_Circuit_MaxVD" msprop:Generator_UserColumnName="Final_Circuit_MaxVD" msprop:Generator_ColumnPropNameInTable="Final_Circuit_MaxVDColumn" msprop:Generator_ColumnPropNameInRow="Final_Circuit_MaxVD" msprop:Generator_ColumnVarNameInTable="columnFinal_Circuit_MaxVD" type="xs:double" minOccurs="0" />
              <xs:element name="Manual" msprop:Generator_UserColumnName="Manual" msprop:Generator_ColumnPropNameInTable="ManualColumn" msprop:Generator_ColumnPropNameInRow="Manual" msprop:Generator_ColumnVarNameInTable="columnManual" type="xs:boolean" minOccurs="0" />
              <xs:element name="Circuit_Path_Mode" msprop:Generator_UserColumnName="Circuit_Path_Mode" msprop:Generator_ColumnPropNameInTable="Circuit_Path_ModeColumn" msprop:Generator_ColumnPropNameInRow="Circuit_Path_Mode" msprop:Generator_ColumnVarNameInTable="columnCircuit_Path_Mode" type="xs:string" minOccurs="0" />
              <xs:element name="Length_Total" msprop:Generator_UserColumnName="Length_Total" msprop:Generator_ColumnPropNameInTable="Length_TotalColumn" msprop:Generator_ColumnPropNameInRow="Length_Total" msprop:Generator_ColumnVarNameInTable="columnLength_Total" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>