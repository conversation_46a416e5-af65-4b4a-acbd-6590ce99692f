﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.UI;
using BecaCommand;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_5.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using TaskDialogIcon = Autodesk.Revit.UI.TaskDialogIcon;


/// 2020.01.01 Version 1.2 Development Start
/// -- 2018.11.05 PowerBIM work in progress
/// -- 2018.11.22 3202 DB/Circuit Check completed
/// -- 2018.11.25 3203 Circuit Length Calc Completed
/// -- 2018.11.27 3204 Error handling for Node > 1 
/// -- 2019.06.02 Add installation method, size 1st and Remainder cables separately, user defined "CableDatabaseNZandAUrev3.xlsx" location
/// -- 2020.01.15 Bug fixes.  EFLI to use operating temp. Add cable installation metod text. Update to use cable database version 4
/// -- 2020.01.30 Add Derating factors. Add 1.5x and 2x discrimination check. 
/// 
/// 2020.01.30 Version 1.3 Technical Verification 
/// -- help form added
/// -- warning messages added
/// -- error message cleanup
/// -- output verifiaction summary added
/// -- cable summary cleanup
/// -- Public Release
/// -- PowerBIM version 4 start
/// 
/// 2020.08 Version 1.4 Release
/// -- Add SC wihstand calcs
/// -- Add advanced settings menu
/// -- Add DB circuit diversity and diversified totals
/// -- Add new beca branding and colour schema.
/// 
/// 2021.02 Version 1.5 Release
/// -- Code Refactoring
/// -- PowerBIM_ProjectInfo added
/// -- PowerBIM_BreakerData added
/// -- PowerBIM_CCTData added
/// -- PowerBIM_DBData added
/// -- PowerBIM_Constants added
/// -- PowerBIM_CableData added
/// -- PowerBIM_FileExport added


namespace MEP.PowerBIM_5.RevitCommands
{
    [Transaction(TransactionMode.Manual)]

    class BecaBedarPowerBIMMain : BecaBaseCommandTelemetry
    {
        // Setup public variables/classes for this application
        public PowerBIM_ProjectInfo projInfo;
        public List<PowerBIM_DBData> DBs;

        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Create document and application instnaces. 
                UIApplication uiapp = commandData.Application;
                UIDocument uidoc = uiapp.ActiveUIDocument;
                var app = uiapp.Application;



                // Start tracker
                _taskLogger.PreTaskStart();

                // Beta Test Warnings
                TaskDialog TD = new TaskDialog("Beca MEP Tools - Power BIM");
                TD.Id = "ID_TD_PowerBIM";
                TD.MainIcon = TaskDialogIcon.TaskDialogIconWarning;
                TD.AllowCancellation = true;
                TD.MainContent = "This is PowerBIM release 5 [REFACTOR] and intended for BETA TESTING ONLY.";
                TD.CommonButtons = TaskDialogCommonButtons.Ok | TaskDialogCommonButtons.Cancel;
                TD.DefaultButton = TaskDialogResult.Ok;
                TaskDialogResult TDres = TD.Show();

                // if user is scared by the beta warning and wants to cancel...
                if (TDres == TaskDialogResult.Cancel)
                {
                    _taskLogger.PostTaskEnd("PowerBim was canceled.");
                    return Autodesk.Revit.UI.Result.Cancelled;
                }

                // create instance of PowerBIM ProjectInfo class for global settings
                projInfo = new PowerBIM_ProjectInfo(uidoc, _taskLogger);

                if (!projInfo.DatabaseArrayIsPopulated)
                {
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Critical Error", "\nUnable to run PowerBIM. Please check that your PowerBIM 5 database files exist!");
                    return Autodesk.Revit.UI.Result.Failed;
                }

                if (projInfo.CriticalErrorMessage != "")
                {
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Critical Error", "Unable to run PowerBIM. Revit is missing the following parameter(s): \n\n" + projInfo.CriticalErrorMessage);
                    _taskLogger.PostTaskEnd("PowerBim Failed to process.");
                    return Autodesk.Revit.UI.Result.Failed;
                }

                //Get DB Panel Schedules
                FilteredElementCollector PanelSchCol = new FilteredElementCollector(projInfo.Document).OfClass(typeof(PanelScheduleView));
                IList<Element> PanelSchs = PanelSchCol.ToElements() as List<Element>;

                // Fill DBs
                DBs = new List<PowerBIM_DBData>();

                int nCount = PanelSchs.Count();
                string progressMessage = "{0} of " + nCount.ToString() + " panels processed...";
                string caption = "Loading Panel Schedules";
                using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, nCount))
                {
                    using (var trans_CCT = new Transaction(projInfo.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
                    {
                        trans_CCT.Start();
                        // Create list of PowerBIM_DBData class for every panel schedule in the project/
                        foreach (PanelScheduleView rqPnlSch in PanelSchs)
                        {
                            Element PnlElem = projInfo.Document.GetElement(rqPnlSch.GetPanel());
                            // create new DB class
                            PowerBIM_DBData x = new PowerBIM_DBData(projInfo, PnlElem);
                            // Initialise all circuits
                            x.Initialise_AllCircuits();
                            // add to list of DBs in project
                            DBs.Add(x);
                            pf.Increment();
                        }
                        trans_CCT.Commit();
                    }
                }

                // sort list list of PowerBIM_DBData classes alphabetical (descending) order
                IOrderedEnumerable<PowerBIM_DBData> orderedDBs = from PowerBIM_DBData DB in DBs orderby DB.Schedule_DB_Name ascending select DB;

                // Check that there is at least 1 DB set up
                if (DBs.Count < 1)
                {
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("PowerBIM", "PowerBIM Error: Your project has no DB schedules yet!");
                    return Autodesk.Revit.UI.Result.Cancelled;
                }

                //Check the current view and get the DB name for that
                string strCurrentPanelName = "";
                if (projInfo.Document.ActiveView.ViewType == Autodesk.Revit.DB.ViewType.PanelSchedule)
                {
                    PanelScheduleView rqCurrentPanelView = projInfo.Document.ActiveView as PanelScheduleView;
                    strCurrentPanelName = projInfo.Document.GetElement(rqCurrentPanelView.GetPanel()).Name;
                }

                // convert enumerable back to list
                DBs = orderedDBs.ToList();

                //Find the position of currently viewed panel schedule, as we want this to be highlighted in the dialogue when we launch powerbim
                int intPosCount = 0;
                foreach (PowerBIM_DBData DB in DBs)
                {
                    if (DB.Schedule_DB_Name == strCurrentPanelName)
                        projInfo.GUI_Gen_DB_Selected_Position = intPosCount;

                    intPosCount++;
                }

                //Initialate the dialogue form object
                //frmPowerBIM_Start PowerBIM_StartDialogue = new frmPowerBIM_Start(DBs, projInfo);
                //PowerBIM_StartDialogue.ShowDialog();

                ModelessPowerBIM_StartFormHandler.ShowForm(DBs, projInfo, _taskLogger);

                // Modeless form


                // If user selects 'close'- this is the only way to exit the application

                // Successfully completed PowerBIM processes!!  
                _taskLogger.PostTaskEnd("Summary.");

                // Return application succeeded
                return Autodesk.Revit.UI.Result.Succeeded;
            }
            catch (Exception e)
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("PowerBIM Failed", e.Message);
                throw;
            }
        }

        public override string GetAddinAuthor()
        {
            return "Tristan Balme";
        }

        public override string GetAddinName()
        {
            return AddinNames.PowerBIM5.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }

        protected override bool CheckUserPermssionToUseTool()
        {
            var licHanlder = Common.BecaLicense.License.BecaLicenseHandler.GetHandler();

            if (!licHanlder.Lic.BedarToolsPowerBim)
            {
                Common.UI.Forms.BecaBaseMessageForm.ShowDialog("License privileges are not enough", "You don't have authority to execute this command");
                return false;
            }
            return true;
        }
    }
}
