﻿using Autodesk.Revit.UI;
using Common.Utilities;
using System.Collections.Generic;
using System.IO;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_5.CoreLogic
{
    class PowerBIM_Calculations
    {

        internal static double Calc_EFLI(PowerBIM_CircuitData cct, double DBEFLiR, double DBEFLiX)
        {
            //
            // ** 2.073 & 2.074 Calculate Circuit EFLi
            //
            //Revised for vectorised addition
            //Revised for operating cable temerature for earth and active 22.1.2020
            double dblRmax = ((cct.Cable_To_First.R_Operating_Active + cct.Cable_To_First.R_Operating_Earth) * cct.LengthClass.Length_To_First * 0.000001) + ((cct.Cable_To_Final.R_Operating_Active + cct.Cable_To_Final.R_Operating_Earth) * (cct.LengthClass.Length_Total - cct.LengthClass.Length_To_First) * 0.000001) + DBEFLiR;

            double dblXmax = ((cct.Cable_To_First.X_Max_Active + cct.Cable_To_First.X_Max_Earth) * cct.LengthClass.Length_To_First * 0.000001) + ((cct.Cable_To_Final.X_Max_Active + cct.Cable_To_Final.X_Max_Earth) * (cct.LengthClass.Length_Total - cct.LengthClass.Length_To_First) * 0.000001) + DBEFLiX;
            return Math.Sqrt((dblRmax * dblRmax) + (dblXmax * dblXmax));
        }

        internal static double Calc_EFLI_To_First(PowerBIM_CircuitData cct, double DBEFLiR, double DBEFLiX)
        {
            //
            // ** 2.073 & 2.074 Calculate Circuit EFLi
            //
            //Revised for vectorised addition
            //Revised for operating cable temerature for earth and active 22.1.2020
            double dblRmax = ((cct.Cable_To_First.R_Operating_Active + cct.Cable_To_First.R_Operating_Earth) * cct.LengthClass.Length_To_First * 0.000001) + DBEFLiR;

            double dblXmax = ((cct.Cable_To_First.X_Max_Active + cct.Cable_To_First.X_Max_Earth) * cct.LengthClass.Length_To_First * 0.000001) + DBEFLiX;
            return Math.Sqrt((dblRmax * dblRmax) + (dblXmax * dblXmax));
        }


        internal static double Calc_FinalCircuitVoltDrop(PowerBIM_CircuitData cct, VoltDropCalculation VDRule)
        {
            //
            // ** 2.085 and 2.086 Calc VDfinalCct
            //
            if (VDRule == VoltDropCalculation.LumpLoad) // Use lump load
            {
                return (cct.Cable_To_First.Z_Operating_Active * cct.LengthClass.Length_To_First * 0.000001 * 2 * cct.GetCurrent()) + (cct.Cable_To_Final.Z_Operating_Active * (cct.LengthClass.Length_Total - cct.LengthClass.Length_To_First) * 0.000001 * 2 * cct.GetCurrent());
            }
            else // use linear depreciating clac [DEFAULT]
            {
                return (cct.Cable_To_First.Z_Operating_Active * cct.LengthClass.Length_To_First * 0.000001 * 2 * cct.GetCurrent()) + ((cct.Cable_To_Final.Z_Operating_Active * (cct.LengthClass.Length_Total - cct.LengthClass.Length_To_First) * 0.000001 * 2 * cct.GetCurrent()) / 2);
            }
        }


        internal static double Calc_FinalCircuitVoltDropPercentage(double calculateVD, int numberOfPoles)
        {
            //
            // Convert VD into percentage value based on number fo poles
            //
            if (numberOfPoles == 3)
                return calculateVD * 0.866 / 400;

            else
                return calculateVD / 230;
        }


        internal static bool Bool_EFLiCheck(PowerBIM_CircuitData CCT)
        {
            //
            // Calculate MAX EFLI
            //

            //**2.071 Pass if RCCB or RCBO
            if (CCT.Breaker.Schedule_Protective_Device == "RCCB" || CCT.Breaker.Schedule_Protective_Device == "RCBO")
            {
                // If device is and RCD, then always pass the EFLI check.
                CCT.CalcRes_Total_EFLi = 0;
                CCT.Breaker.EFLI_Max = 0;
                return true;
            }
            else
            {
                //
                // ** 2.072 Find EFLIMax
                //
                if (CCT.CCT_Clearing_Time == 400)                   // if we doing 400ms clearing time
                    CCT.Breaker.Find_EFLIMax(false);

                else if (CCT.CCT_Clearing_Time == 5000)             // if we doing 5s clearing time
                    CCT.Breaker.Find_EFLIMax(true);

                else
                    CCT.Breaker.EFLI_Max = 0;                       // We have had a problem assigning clearing times

                //
                // ** 2.074 and 2.075 Calculate Circuit EFLi
                //
                if (CCT.Breaker.Schedule_Protective_Device == "MCB" && (CCT.Schedule_RCD?.ToUpper().Contains("LOCAL") ?? false))
                    CCT.CalcRes_Total_EFLi = Calc_EFLI_To_First(CCT, CCT.DB.EFLI_R, CCT.DB.EFLI_X);
                else
                    CCT.CalcRes_Total_EFLi = Calc_EFLI(CCT, CCT.DB.EFLI_R, CCT.DB.EFLI_X);

                // check we are less than max allowable EFLI
                if (CCT.CalcRes_Total_EFLi <= CCT.Breaker.EFLI_Max)
                    return true;

                else
                    return false;
            }
        }


        internal static bool Bool_CableVoltDropCheck(double finalCircuitCalculatedVDPerc, double finalCircuitMaxVDPerc)
        {
            if (finalCircuitCalculatedVDPerc <= finalCircuitMaxVDPerc)
                return true;
            else
                return false;
        }


        internal static bool Bool_SystemVoltDropCheck(double finalCircuitVDPerc, double dbVDPerc, double SystemMaxVDPerc)
        {
            // Check total system VD is less than 5% or 7%
            if (finalCircuitVDPerc + dbVDPerc <= SystemMaxVDPerc)
                return true;
            else
                return false;
        }


        internal static bool Bool_CableCurrentRatingCheck(double cableRatedCurrent, double tripRating, double deratingFactor)
        {
            //** 2.06 Check Cable Rating vs Circuit Trip Rating for 1st and Remainder

            if (tripRating <= (cableRatedCurrent * deratingFactor)) // added derating factor
                return true;
            else
                return false;
        }


        internal static bool Bool_ShortCircuitCheck(double dblMaxSCWithstand, double dblK2S2)
        {
            //**2.09
            if (dblK2S2 > dblMaxSCWithstand)
                return true;

            else
                return false;
        }




        internal static bool PowerBIMCircuitCheckEngine(PowerBIM_CircuitData CCT)
        {
            if (!CCT.CCT_Is_Spare_Or_Space)
            {
                // create counter variable
                int count = 0;

                // boolean addition. add 1 for every pass result. 
                count += (CCT.CircuitCheck1_Data() ? 1 : 0);
                count += (CCT.CircuitCheck2_CableToFirst() ? 1 : 0);
                count += (CCT.CircuitCheck3_CableToFinal() ? 1 : 0);
                count += (CCT.CircuitCheck4_Discrimination() ? 1 : 0);                 //  Check Brewaker Discrimination 
                count += (CCT.CircuitCheck5_OverloadCurrent() ? 1 : 0);                //  Check Overload Current 
                count += (CCT.CircuitCheck6_RatedCurrent_CableToFirst() ? 1 : 0);      //  TODO
                count += (CCT.CircuitCheck7_RatedCurrent_CableToFinal() ? 1 : 0);      //  TODO
                count += (CCT.CircuitCheck8_CheckEFLI() ? 1 : 0);                        //  Check Earth Fault Loop Impedance 
                count += (CCT.CircuitCheck9_CircuitVoltDrop() ? 1 : 0);                  //  Check Circuit Volt Drop 
                count += (CCT.CircuitCheck10_SystemVoltDrop() ? 1 : 0);                  //  Check System Volt Drop
                count += (CCT.CircuitCheck11_ShortCircuitCable1() ? 1 : 0);              //  Check Short Circuit Withstand 
                count += (CCT.CircuitCheck12_ShortCircuitCable2() ? 1 : 0);              //  Check Short Circuit Withstand 

                // if we have all 12 tests return as a pass
                if (count == 12)
                    return true;

                // else, if even 1 test fails, return false. 
                else
                    return false;
            }
            else
                return false;
        }
    
    }
}
