﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitElementsCreators;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_5.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;

namespace MEP.PowerBIM_1._5.UI.ModelessRevitForm
{
    /// <summary>
    ///   A class with methods to execute requests made by the dialog user.
    /// </summary>
    /// 
    public class RequestHandler : IExternalEventHandler
    {

        #region Fields

        EditCircuitPathClicker _editCircuitPathClicker;
        EditDBPathClicker _editDbPathClicker;

        BecaActivityLoggerData _logger;

        // The value of the latest request made by the modeless form 
        Request _request = new Request();

        #endregion

        #region Properties

        /// <summary>
        /// A public property to access the current request value
        /// </summary>
        public Request Request
        {
            get { return _request; }
        }

        public BecaActivityLoggerData Logger { get => _logger; }

        #endregion

        #region Constructors

        public RequestHandler(UIApplication uiApp, BecaActivityLoggerData logger)
        {
            _logger = logger;
            _editCircuitPathClicker = EditCircuitPathClicker.Create(uiApp);
            _editDbPathClicker = EditDBPathClicker.Create(uiApp);
        }

        #endregion

        #region Methods

        #region IExternalEventHandler Methods

        /// <summary>
        ///   A method to identify this External Event Handler
        /// </summary>
        public String GetName()
        {
            return "Firza Utama";
        }


        /// <summary>
        ///   The top method of the event handler.
        /// </summary>
        /// <remarks>
        ///   This is called by Revit after the corresponding
        ///   external event was raised (by the modeless form)
        ///   and Revit reached the time at which it could call
        ///   the event's handler (i.e. this object)
        /// </remarks>
        /// <returns>Status</returns>
        public void Execute(UIApplication uiapp)
        {
            try
            {
                var rId = Request.Take();
                switch (rId)
                {
                    case RequestId.None:
                        {
                            return;  // no request at this time -> we can leave immediately
                        }

                    case RequestId.SavePowerBIM:
                        {
                            // save settings
                            ModelessPowerBIM_StartFormHandler.SaveSettings();
                            // Commit settings to revit
                            ModelessPowerBIM_StartFormHandler.CommitProjectInfo();

                            ModelessPowerBIM_StartFormHandler.WakeFormUp();

                            break;
                        }

                    case RequestId.SaveSettings:
                        {
                            // save settings
                            ModelessPowerBIM_StartFormHandler.SaveSettings();
                            // Commit settings to revit
                            ModelessPowerBIM_StartFormHandler.CommitProjectInfo();
                            //regenerate circuit properties
                            ModelessPowerBIM_StartFormHandler.RegenerateCircuitProperties();
                            // Flag that update is required
                            ModelessPowerBIM_StartFormHandler.UpdaterRequired_All();

                            ModelessPowerBIM_StartFormHandler.WakeFormUp();

                            break;
                        }

                    case RequestId.UpdaterRequired_All:
                        {
                            ModelessPowerBIM_StartFormHandler.UpdaterRequired_All();
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();

                            break;
                        }

                    case RequestId.CommitProjectInfo:
                        {
                            ModelessPowerBIM_StartFormHandler.CommitProjectInfo();
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();

                            break;
                        }

                    case RequestId.RegenerateCircuitProperties:
                        {
                            ModelessPowerBIM_StartFormHandler.RegenerateCircuitProperties();

                            break;
                        }

                    case RequestId.IterateCircuits:
                        {
                            ModelessPowerBIM_StartFormHandler.RunAutoSizer();
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();
                            break;
                        }

                    case RequestId.WriteLightingToSchedule:
                        {
                            ModelessPowerBIM_StartFormHandler.WriteLightingToSchedule();
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();
                            break;
                        }

                    case RequestId.WritePowerToSchedule:
                        {
                            ModelessPowerBIM_StartFormHandler.WritePowerToSchedule();
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();
                            break;
                        }

                    case RequestId.WriteOtherToSchedule:
                        {
                            ModelessPowerBIM_StartFormHandler.WriteOtherToSchedule();
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();
                            break;
                        }

                    case RequestId.RunCircuitCheckManual:
                        {
                            ModelessPowerBIM_StartFormHandler.RunCircuitCheckManual();
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();
                            break;
                        }

                    case RequestId.DBEdit_SubmitData:
                        {
                            ModelessPowerBIM_DbEditFormHandler.DBEdit_SubmitData();
                            ModelessPowerBIM_DbEditFormHandler.WakeFormUpDbEdit();

                            break;
                        }

                    case RequestId.WakeFormUpCircuitEditEnhanced:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();

                            break;
                        }

                    case RequestId.WakeFormUpDbEdit:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();

                            break;
                        }

                    case RequestId.OpenPathCustomisingView:
                        {
                            bool viewCreated;
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.OpenPathCustomisingView(out viewCreated);

                            if (viewCreated)
                            {

                                ModelessPowerBIM_CircuitEditEnhancedFormHandler.EnableEditCircuitPath();
                                _editCircuitPathClicker.Click();
                            }

                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();

                            break;
                        }
                    case RequestId.OpenPathCustomisingViewForDB:
                        {
                            if (ModelessPowerBIM_DbEditFormHandler.OpenPathCustomisingView())
                            {
                                ModelessPowerBIM_DbEditFormHandler.EnableEditDBPath(uiapp.ActiveUIDocument);
                                _editDbPathClicker.Click();
                            }

                            ModelessPowerBIM_DbEditFormHandler.WakeFormUpDbEdit();

                            break;
                        }
                    case RequestId.ActivateEdithPathView:
                        {
                            var viewName = PowerBIM_3DViewLogic.GetUserViewName();
                            var view = BecaRevitUtilities.Collectors.ElementCollectorUtility.FindElementByName<View>(uiapp.ActiveUIDocument.Document, viewName);
                            if (view == null)
                            {
                                using (Transaction tx = new Transaction(uiapp.ActiveUIDocument.Document, "Create PowerBIM View"))
                                {
                                    tx.Start();
                                    view = PowerBIM_3DViewLogic.CreatePowerBimView(uiapp.ActiveUIDocument.Document, _logger);
                                    tx.Commit();
                                }
                            }
                            if (view != null && uiapp.ActiveUIDocument.ActiveView.Name != viewName)
                            {
                                uiapp.ActiveUIDocument.ActiveView = view;
                            }
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            ModelessPowerBIM_DbEditFormHandler.WakeFormUpDbEdit();
                            break;
                        }
                    case RequestId.Initialise_AllCircuits:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.Initialise_AllCircuits();
                            break;
                        }

                    case RequestId.AutoCalc:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.AutoCalc();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }

                    case RequestId.RecalcAndRefreshCircuitToForm:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.MakePendingData();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }

                    case RequestId.RecalcAndRefreshLengthToFormClick:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.MakePendingData();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }

                    case RequestId.SaveCircuitEditEnhanced:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.SaveCircuitEditEnhanced();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }

                    case RequestId.RevertToOldSave:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.RevertToOldSave();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }

                    case RequestId.Refresh_DerrivedCircuitProperties:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.MakePendingData();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }

                    case RequestId.WakeFormUpStartForm:
                        {
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();
                            break;
                        }

                    case RequestId.Commit_AdvancedSettings:
                        {
                            ModelessPowerBIM_AdvancedSettingsFormHandler.Commit_AdvancedSettings();

                            // save settings
                            ModelessPowerBIM_StartFormHandler.SaveSettings();
                            // Commit settings to revit
                            ModelessPowerBIM_StartFormHandler.CommitProjectInfo();
                            //regenerate circuit properties
                            ModelessPowerBIM_StartFormHandler.RegenerateCircuitProperties();
                            // Flag that update is required
                            ModelessPowerBIM_StartFormHandler.UpdaterRequired_All();

                            break;
                        }

                    case RequestId.Commit_DBSettings:
                        {
                            ModelessPowerBIM_DBSettingsFormHandler.Commit_AdvancedSettings();

                            // save settings
                            ModelessPowerBIM_StartFormHandler.SaveSettings();
                            // Commit settings to revit
                            ModelessPowerBIM_StartFormHandler.CommitProjectInfo();
                            //regenerate circuit properties
                            ModelessPowerBIM_StartFormHandler.RegenerateCircuitProperties();
                            // Flag that update is required
                            ModelessPowerBIM_StartFormHandler.UpdaterRequired_All();
                            break;
                        }

                    case RequestId.SetCircuitLengthManual:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.MakePendingData();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }
                    case RequestId.SetDbLengthManual:
                        {
                            ModelessPowerBIM_DbEditFormHandler.SetCircuitLengthManual();
                            ModelessPowerBIM_DbEditFormHandler.WakeFormUpDbEdit();
                            break;
                        }
                    case RequestId.RecalcAndRefreshLengthToDbForm:
                        {
                            ModelessPowerBIM_DbEditFormHandler.RecalcAndRefreshLengthToDbForm();
                            ModelessPowerBIM_DbEditFormHandler.WakeFormUpDbEdit();
                            break;
                        }
                    case RequestId.SetFirstLengthManual:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.MakePendingData();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }

                    case RequestId.SetTotalLengthManual:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.MakePendingData();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }

                    case RequestId.WriteLengthAfterUserInputToCircuitParameter:
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.MakePendingData();
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.WakeFormUpCircuitEditEnhanced();
                            break;
                        }
                    case RequestId.ExportCircuitPathImages:
                        {
                            ModelessPowerBIM_StartFormHandler.ExportCircuitPathImages(uiapp.ActiveUIDocument, _logger);
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();

                            break;
                        }
                    case RequestId.SetManualLock:
                        {
                            ModelessPowerBIM_StartFormHandler.SetManualLock(uiapp.ActiveUIDocument, _logger);
                            ModelessPowerBIM_StartFormHandler.WakeFormUp();

                            break;
                        }
                    default:
                        {
                            // some kind of a warning here should
                            // notify us about an unexpected request 
                            break;
                        }
                }


            }
            catch (Exception ex)
            {
                _logger.Log("Exception: " + ex.Message, LogType.Error);
            }
            finally
            {
                //ModelessPowerBIM_StartFormHandler.WakeFormUp();
            }

            return;
        }

        #endregion

        #region Logic Helpers

        #endregion

        #endregion

    }
}
