﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_6.CableManagement
{
    internal class PB_Conductivity
    {
        public PB_Cable parentCable { get; private set; }
        public string conductorType { get; private set; }
        public PB_CableProperties cableProperties { get; private set; }
        public double sizeMm2 { get; private set; }
        public PB_Reactance reactance { get; private set; }
        public PB_Resistance resistance { get; private set; }

        public PB_Conductivity(PB_Cable parentCable, string conductorType, PB_CableProperties cableProperties)
        {
            this.parentCable = parentCable;
            this.conductorType = conductorType;
            this.cableProperties = cableProperties;

            if (this.conductorType == "Active")
            {
                sizeMm2 = this.parentCable.activeSphMm2;
            }
            else if (this.conductorType == "Earth")
            {
                sizeMm2 = this.parentCable.earthSpeMm2;
            }
            else
            {
                Console.WriteLine("Invalid Conductor Type in PB_Conductivity!");
            }

            reactance = new PB_Reactance(this.parentCable, this, this.cableProperties);
            resistance = new PB_Resistance(this.parentCable, this, this.cableProperties);
        }
    }
}
