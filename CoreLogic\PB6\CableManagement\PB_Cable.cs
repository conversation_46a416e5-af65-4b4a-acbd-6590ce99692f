﻿using System.Data;

namespace MEP.PowerBIM_6.CableManagement
{
    internal class PB_Cable
    {
        // Public properties with private setters
        public string country { get; private set; }
        public PB_CableProperties cableProperties { get; private set; }
        public string id { get; private set; }
        public string cableArrangement { get; private set; }
        public string cores { get; private set; }
        public double activeSphMm2 { get; private set; }
        public double earthSpeMm2 { get; private set; }
        public int phase { get; private set; }
        public string insulationMaterial { get; private set; }
        public double ratedCurrent { get; private set; }
        public string conductorMaterial { get; private set; }
        public bool isFlexible { get; private set; }
        public string coreType { get; private set; }
        public string conductorArrangement { get; private set; }
        public PB_Current currentInstallationMethods { get; private set; }
        public PB_Conductivity activeConductivity { get; private set; }
        public PB_Conductivity earthConductivity { get; private set; }
        public string name { get; private set; }

        // Constructor
        public PB_Cable(string country, DataRow row, PB_CableProperties cableProperties)
        {
            this.country = country;
            this.cableProperties = cableProperties;
            this.id = row[0].ToString();
            this.cableArrangement = row[2].ToString();
            this.cores = row[3].ToString();
            this.activeSphMm2 = Convert.ToDouble(row[4]);
            this.earthSpeMm2 = Convert.ToDouble(row[5]);
            this.phase = Convert.ToInt32(row[6]);
            this.insulationMaterial = row[7].ToString();
            this.ratedCurrent = Convert.ToDouble(row[8]);
            this.conductorMaterial = row[9].ToString();
            this.isFlexible = row[10].ToString() == "Flexible" ? true : false;
            this.coreType = row[11].ToString();
            this.conductorArrangement = row[12].ToString();
            this.currentInstallationMethods = new PB_Current(this, this.cableProperties);
            this.activeConductivity = new PB_Conductivity(this, "Active", this.cableProperties);
            this.earthConductivity = new PB_Conductivity(this, "Earth", this.cableProperties);
            this.name = row[13].ToString();
        }
    }
}
