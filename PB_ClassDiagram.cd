﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="MEP.PowerBIM_5.CoreLogic.PowerBIM_DBData" Collapsed="true">
    <Position X="4.5" Y="2" Width="2.25" />
    <Compartments>
      <Compartment Name="Methods" Collapsed="true" />
    </Compartments>
    <AssociationLine Name="CCTs" Type="MEP.PowerBIM_5.CoreLogic.PowerBIM_CircuitData" FixedToPoint="true">
      <Path>
        <Point X="4.938" Y="2.562" />
        <Point X="4.938" Y="4.25" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="Project_Info" Type="MEP.PowerBIM_5.CoreLogic.PowerBIM_ProjectInfo" FixedFromPoint="true">
      <Path>
        <Point X="6.75" Y="2.125" />
        <Point X="8.75" Y="2.125" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>QBBbCZEC7rwACEA0LLKwCCmfBAX4CmlQFuIWaNiCjBY=</HashCode>
      <FileName>CoreLogic\PowerBIM_DBdata.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Project_Info" />
      <Property Name="LengthClass" />
    </ShowAsAssociation>
    <ShowAsCollectionAssociation>
      <Property Name="CCTs" />
      <Property Name="LockedCircuits" />
    </ShowAsCollectionAssociation>
  </Class>
  <Class Name="MEP.PowerBIM_5.CoreLogic.PowerBIM_CircuitData" Collapsed="true">
    <Position X="4.5" Y="4.25" Width="2.25" />
    <Compartments>
      <Compartment Name="Fields" Collapsed="true" />
      <Compartment Name="Methods" Collapsed="true" />
    </Compartments>
    <TypeIdentifier>
      <HashCode>QAH6TGjCQYIX5FcUyf7o/ZPJAZN5DE9Ekqgqi58FrRU=</HashCode>
      <FileName>CoreLogic\PowerBIM_CircuitData.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="projInfo" />
    </ShowAsAssociation>
  </Class>
  <Class Name="MEP.PowerBIM_5.CoreLogic.PowerBIM_ProjectInfo" Collapsed="true">
    <Position X="8.75" Y="2" Width="1.75" />
    <TypeIdentifier>
      <HashCode>rkxQYQHDJgMMDkCpnpnmAGinEUqisMCT4ggyTgqBRCk=</HashCode>
      <FileName>CoreLogic\PowerBIM_ProjectInfo.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="MEP.PowerBIM_5.CoreLogic.PowerBIM_CableData" Collapsed="true">
    <Position X="8" Y="4.25" Width="1.75" />
    <AssociationLine Name="Circuit" Type="MEP.PowerBIM_5.CoreLogic.PowerBIM_CircuitData" FixedFromPoint="true">
      <Path>
        <Point X="8" Y="4.625" />
        <Point X="6.75" Y="4.625" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAAAhSMCACCAxQDAAQEqICASAIABYADEAAAQEAECAA=</HashCode>
      <FileName>CoreLogic\PowerBIM_CableData.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Project_Info" />
      <Property Name="Circuit" />
    </ShowAsAssociation>
  </Class>
  <Class Name="MEP.PowerBIM_5.CoreLogic.PowerBIM_BreakerData" Collapsed="true">
    <Position X="2" Y="5" Width="2" />
    <TypeIdentifier>
      <HashCode>IAIAQAgAYAABgDAAUAACAAAIAAMAgEAAIoAAB5IADBA=</HashCode>
      <FileName>CoreLogic\PowerBIM_BreakerData.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Field Name="projInfo" />
      <Property Name="DB" />
    </ShowAsAssociation>
  </Class>
  <Class Name="MEP.PowerBIM_5.CoreLogic.PowerBIM_DBRoute" Collapsed="true">
    <Position X="2.25" Y="1" Width="2" />
    <TypeIdentifier>
      <HashCode>AAAAAAIgAAAAAAAAAAAAAEAAAAAAAEAAAAAAQAABBIA=</HashCode>
      <FileName>CoreLogic\PowerBIM_DBRoute.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="MEP.PowerBIM_5.RevitCommands.BecaPowerBIMMain" Collapsed="true">
    <Position X="6.25" Y="0.5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAACAAAACAQAAAAQAAAAAAAAAAAAACAAAAAARBA=</HashCode>
      <FileName>RevitCommands\BecaPowerBIMMain.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Field Name="projInfo" />
    </ShowAsAssociation>
    <ShowAsCollectionAssociation>
      <Field Name="DBs" />
    </ShowAsCollectionAssociation>
  </Class>
  <Class Name="MEP.PowerBIM_5.CoreLogic.PowerBIM_3DViewLogic" Collapsed="true">
    <Position X="11" Y="0.5" Width="2" />
    <TypeIdentifier>
      <HashCode>AAAAEQACQAABQAAAAQAQAAAAhAAQAAAAAAAAAAAABEg=</HashCode>
      <FileName>CoreLogic\PowerBIM_3DViewLogic.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="projInfo" />
    </ShowAsAssociation>
  </Class>
  <Font Name="Segoe UI" Size="9" />
</ClassDiagram>