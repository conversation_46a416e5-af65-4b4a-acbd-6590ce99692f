﻿
namespace MEP.PowerBIM_6.CableManagement
{
    internal class PB_CableManager
    {
        public string countryStandard { get; private set; }
        public bool isCustom { get; private set; }
        public PB_CableProperties cableProperties { get; private set; }
        public Dictionary<string, PB_Cable> cables { get; private set; }

        public PB_CableManager(string countryStandard, bool isCustom = false)
        {
            this.countryStandard = countryStandard;
            this.isCustom = isCustom;
            cableProperties = new PB_CableProperties(countryStandard, isCustom);
            Console.WriteLine("Manager Initialized");
            cables = cableProperties.PopulateCables();
        }
    }
}
