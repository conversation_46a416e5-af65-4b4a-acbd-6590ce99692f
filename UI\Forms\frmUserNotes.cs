﻿using Common.UI.Forms;
using MEP.PowerBIM_5.CoreLogic;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmUserNotes : BecaBaseForm
    {
        PowerBIM_DBData _db;
        Document _doc;

        public bool NotesEmpty { get; set; } = true;

        public frmUserNotes(PowerBIM_DBData db)
        {
            InitializeComponent();

            _db = db;
            _doc = _db.Project_Info.Document;
            TitleText = _db.DB_Element.Name;
        }

        private void btn_Save_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(rtb_Notes.Text))
            {
                try
                {
                    using (var tr = new Transaction(_doc, $"Save {_db.DB_Element.Name} notes"))
                    {
                        tr.Start();
                        _db.DB_Element.LookupParameter("Beca Inst Use")?.Set(rtb_Notes.Text);
                        tr.Commit();
                    }
                    NotesEmpty = false;
                }
                catch (Exception ex)
                {
                    if (!string.IsNullOrWhiteSpace(rtb_Notes.Text))
                    {
                        NotesEmpty = false;
                    }
                    MessageBox.Show($"Error saving notes: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                
            }
            
        }

        private void btn_Cancel_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(rtb_Notes.Text))
            {
                NotesEmpty = false;
            }
        }
    }
}
