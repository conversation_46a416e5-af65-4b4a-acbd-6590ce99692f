﻿using Microsoft.Data.SqlClient;
using System.Data;
using System.Text.RegularExpressions;

namespace MEP.PowerBIM_6.Database
{
    public class PB_DatabaseClient : IDisposable
    {
        private SqlConnection connection;

        public PB_DatabaseClient()
        {
            connection = null;
        }

        public void Connect()
        {
            string server = "devabotdbserver.database.windows.net";
            string database = "PowerBIM";
            string user = "devabotadmin";
            string password = "C@ll@gh@n";

            SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder();
            builder.DataSource = server;
            builder.InitialCatalog = database;
            builder.UserID = user;
            builder.Password = password;

            try
            {
                connection = new SqlConnection(builder.ConnectionString);
                connection.Open();
                Console.WriteLine("Connection successful");
            }
            catch (SqlException e)
            {
                PrintError($"Error connecting to SQL Server: {e.Message}");
                connection = null;
            }
        }

        public void InsertRowToTable(DataRow row, string tableName, string[] columnOrder)
        {
            if (connection == null)
            {
                PrintError("No connection established. Call Connect() first.");
                return;
            }

            SqlCommand command = connection.CreateCommand();
            try
            {
                // Validate that all expected columns are in the provided row
                string missingColumns = string.Join(", ", columnOrder);
                foreach (string column in columnOrder)
                {
                    if (!row.Table.Columns.Contains(column))
                    {
                        throw new ArgumentException($"The following columns are missing from the row data: {column}");
                    }
                }

                // Reorder the row data to match the specified column order
                object[] values = new object[columnOrder.Length];
                for (int i = 0; i < columnOrder.Length; i++)
                {
                    values[i] = row[columnOrder[i]];
                }

                // Build the INSERT query
                string columns = string.Join(", ", columnOrder);
                string placeholders = string.Join(", ", new string[columnOrder.Length].Select(_ => "?"));
                string sql = $"INSERT INTO {tableName} ({columns}) VALUES ({placeholders})";

                // Execute the query
                command.CommandText = sql;
                command.Parameters.AddRange(values);
                command.ExecuteNonQuery();

                Console.WriteLine($"Inserted row into '{tableName}' with column order: {string.Join(", ", columnOrder)}.");
            }
            catch (SqlException e)
            {
                PrintError($"Error inserting data into {tableName}: {e.Message}");
            }
            catch (ArgumentException ex)
            {
                PrintError(ex.Message);
            }
            finally
            {
                command.Dispose();
            }
        }

        public DataTable GetAllRowsFromTableViaStandard(string tableName, string standardFilter = "%%")
        {
            if (connection == null)
            {
                PrintError("No connection established. Call Connect() first.");
                return new DataTable();  // Return an empty DataTable if connection fails
            }

            SqlCommand command = connection.CreateCommand();
            try
            {
                // Fetch all rows from the specified table where Standard matches the pattern
                string sql = $"SELECT * FROM {tableName} WHERE Standard LIKE @StandardFilter";
                command.CommandText = sql;
                command.Parameters.AddWithValue("@StandardFilter", standardFilter);

                // Fetch all rows
                SqlDataAdapter adapter = new SqlDataAdapter(command);
                DataTable dataTable = new DataTable();
                adapter.Fill(dataTable);

                // Log the shape of the fetched data
                Console.WriteLine($"Fetched {dataTable.Rows.Count} rows.");

                // Ensure data integrity: Check if rows contain the expected number of elements
                if (dataTable.Rows.Count > 0 && dataTable.Columns.Count != dataTable.Rows[0].ItemArray.Length)
                {
                    throw new ArgumentException($"Shape mismatch! Number of columns ({dataTable.Columns.Count}) doesn't align with fetched data ({dataTable.Rows[0].ItemArray.Length} per row).");
                }

                return dataTable;
            }
            catch (SqlException e)
            {
                PrintError($"Error fetching rows from {tableName}: {e.Message}");
                return new DataTable();  // Return an empty DataTable on error
            }
            catch (ArgumentException ex)
            {
                PrintError(ex.Message);
                return new DataTable();  // Handle shape mismatch error
            }
            finally
            {
                command.Dispose();
            }
        }

        public async Task<DataTable> GetAllRowsFromTableViaStandardAsync(string tableName, string standardFilter = "%%")
        {
            if (connection == null)
            {
                PrintError("No connection established. Call Connect() first.");
                return new DataTable(); // Return an empty DataTable if connection fails
            }

            // Create the SQL command
            using (SqlCommand command = connection.CreateCommand())
            {
                try
                {
                    // Define the SQL query with a parameter
                    string sql = $"SELECT * FROM {tableName} WHERE Standard LIKE @StandardFilter";
                    command.CommandText = sql;
                    command.Parameters.AddWithValue("@StandardFilter", standardFilter);

                    // Use an async SqlDataReader to fetch rows
                    using (SqlDataReader reader = await command.ExecuteReaderAsync().ConfigureAwait(false))
                    {
                        // Load the results into a DataTable
                        DataTable dataTable = new DataTable();
                        dataTable.Load(reader); // This is synchronous, but loading from an already-read DataReader is efficient.

                        // Log the shape of the fetched data
                        Console.WriteLine($"Fetched {dataTable.Rows.Count} rows.");

                        // Ensure data integrity: Check for column-to-row mismatch
                        if (dataTable.Rows.Count > 0 && dataTable.Columns.Count != dataTable.Rows[0].ItemArray.Length)
                        {
                            throw new ArgumentException(
                                $"Shape mismatch! Number of columns ({dataTable.Columns.Count}) doesn't align with fetched data ({dataTable.Rows[0].ItemArray.Length} per row)."
                            );
                        }

                        // Return the populated DataTable
                        return dataTable;
                    }
                }
                catch (SqlException e)
                {
                    PrintError($"Error fetching rows from {tableName}: {e.Message}");
                    return new DataTable(); // Return an empty DataTable on error
                }
                catch (ArgumentException ex)
                {
                    PrintError(ex.Message);
                    return new DataTable(); // Handle shape mismatch error
                }
            }
        }

        public DataTable GetAllRowsFromTable(string tableName)
        {
            if (connection == null)
            {
                PrintError("No connection established. Call Connect() first.");
                return new DataTable();  // Return an empty DataTable if connection fails
            }

            SqlCommand command = connection.CreateCommand();
            try
            {
                // Fetch all rows from the specified table
                string sql = $"SELECT * FROM {tableName}";
                command.CommandText = sql;

                // Fetch all rows
                SqlDataAdapter adapter = new SqlDataAdapter(command);
                DataTable dataTable = new DataTable();
                adapter.Fill(dataTable);

                // Log the shape of the fetched data
                // Console.WriteLine($"Fetched {dataTable.Rows.Count} rows.");

                // Ensure data integrity: Check if rows contain the expected number of elements
                if (dataTable.Rows.Count > 0 && dataTable.Columns.Count != dataTable.Rows[0].ItemArray.Length)
                {
                    throw new ArgumentException($"Shape mismatch! Number of columns ({dataTable.Columns.Count}) doesn't align with fetched data ({dataTable.Rows[0].ItemArray.Length} per row).");
                }

                return dataTable;
            }
            catch (SqlException e)
            {
                PrintError($"Error fetching rows from {tableName}: {e.Message}");
                return new DataTable();  // Return an empty DataTable on error
            }
            catch (ArgumentException ex)
            {
                PrintError(ex.Message);
                return new DataTable();  // Handle shape mismatch error
            }
            finally
            {
                command.Dispose();
            }
        }

        public async Task<DataTable> GetAllRowsFromTableAsync(string tableName)
        {
            if (connection == null)
            {
                PrintError("No connection established. Call Connect() first.");
                return new DataTable(); // Return an empty DataTable if connection fails
            }

            // Create the SQL command
            using (SqlCommand command = connection.CreateCommand())
            {
                try
                {
                    // Define the SQL query
                    string sql = $"SELECT * FROM {tableName}";
                    command.CommandText = sql;

                    // Use async SqlDataReader to fetch rows
                    using (SqlDataReader reader = await command.ExecuteReaderAsync().ConfigureAwait(false))
                    {
                        // Load the results into a DataTable
                        DataTable dataTable = new DataTable();
                        dataTable.Load(reader);  // Synchronous but efficient for loading the data already fetched

                        // Log the shape of the fetched data
                        Console.WriteLine($"Fetched {dataTable.Rows.Count} rows.");

                        // Ensure data integrity: Check for column-to-row mismatch
                        if (dataTable.Rows.Count > 0 && dataTable.Columns.Count != dataTable.Rows[0].ItemArray.Length)
                        {
                            throw new ArgumentException(
                                $"Shape mismatch! Number of columns ({dataTable.Columns.Count}) doesn't align with fetched data ({dataTable.Rows[0].ItemArray.Length} per row)."
                            );
                        }

                        // Return the populated DataTable
                        return dataTable;
                    }
                }
                catch (SqlException e)
                {
                    PrintError($"Error fetching rows from {tableName}: {e.Message}");
                    return new DataTable(); // Return an empty DataTable on error
                }
                catch (ArgumentException ex)
                {
                    PrintError(ex.Message);
                    return new DataTable(); // Handle shape mismatch error
                }
            }
        }

        public void Close()
        {
            if (connection != null)
            {
                connection.Close();
                Console.WriteLine("Connection closed");
            }
        }

        private void PrintError(string message)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine(message);
            Console.ResetColor();
        }

        public void Dispose()
        {
            this.Close();
        }
    }
}
