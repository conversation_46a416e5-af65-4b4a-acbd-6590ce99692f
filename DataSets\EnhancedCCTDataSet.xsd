﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="EnhancedCCTDataSet" targetNamespace="http://tempuri.org/EnhancedCCTDataSet.xsd" xmlns:mstns="http://tempuri.org/EnhancedCCTDataSet.xsd" xmlns="http://tempuri.org/EnhancedCCTDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="EnhancedCCTDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="EnhancedCCTDataSet" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="EnhancedCCTDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="EnhancedCCTTable" msprop:Generator_RowEvHandlerName="EnhancedCCTTableRowChangeEventHandler" msprop:Generator_RowDeletedName="EnhancedCCTTableRowDeleted" msprop:Generator_RowDeletingName="EnhancedCCTTableRowDeleting" msprop:Generator_RowEvArgName="EnhancedCCTTableRowChangeEvent" msprop:Generator_TablePropName="EnhancedCCTTable" msprop:Generator_RowChangedName="EnhancedCCTTableRowChanged" msprop:Generator_UserTableName="EnhancedCCTTable" msprop:Generator_RowChangingName="EnhancedCCTTableRowChanging" msprop:Generator_RowClassName="EnhancedCCTTableRow" msprop:Generator_TableClassName="EnhancedCCTTableDataTable" msprop:Generator_TableVarName="tableEnhancedCCTTable">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Circuit_Number" msprop:Generator_ColumnPropNameInTable="Circuit_NumberColumn" msprop:Generator_ColumnPropNameInRow="Circuit_Number" msprop:Generator_UserColumnName="Circuit_Number" msprop:Generator_ColumnVarNameInTable="columnCircuit_Number" type="xs:string" minOccurs="0" />
              <xs:element name="Device_Rating" msprop:Generator_ColumnPropNameInTable="Device_RatingColumn" msprop:Generator_ColumnPropNameInRow="Device_Rating" msprop:Generator_UserColumnName="Device_Rating" msprop:Generator_ColumnVarNameInTable="columnDevice_Rating" type="xs:double" default="0" />
              <xs:element name="Device_Curve_Type" msprop:Generator_ColumnPropNameInTable="Device_Curve_TypeColumn" msprop:Generator_ColumnPropNameInRow="Device_Curve_Type" msprop:Generator_UserColumnName="Device_Curve_Type" msprop:Generator_ColumnVarNameInTable="columnDevice_Curve_Type" type="xs:string" minOccurs="0" />
              <xs:element name="Protection_Device" msprop:Generator_ColumnPropNameInTable="Protection_DeviceColumn" msprop:Generator_ColumnPropNameInRow="Protection_Device" msprop:Generator_UserColumnName="Protection_Device" msprop:Generator_ColumnVarNameInTable="columnProtection_Device" type="xs:string" minOccurs="0" />
              <xs:element name="RCD_Protection" msprop:Generator_ColumnPropNameInTable="RCD_ProtectionColumn" msprop:Generator_ColumnPropNameInRow="RCD_Protection" msprop:Generator_UserColumnName="RCD_Protection" msprop:Generator_ColumnVarNameInTable="columnRCD_Protection" type="xs:string" minOccurs="0" />
              <xs:element name="Other_Controls" msprop:Generator_ColumnPropNameInTable="Other_ControlsColumn" msprop:Generator_ColumnPropNameInRow="Other_Controls" msprop:Generator_UserColumnName="Other_Controls" msprop:Generator_ColumnVarNameInTable="columnOther_Controls" type="xs:string" minOccurs="0" />
              <xs:element name="Cable_To_First_Circuit_Component" msprop:Generator_ColumnPropNameInTable="Cable_To_First_Circuit_ComponentColumn" msprop:Generator_ColumnPropNameInRow="Cable_To_First_Circuit_Component" msprop:Generator_UserColumnName="Cable_To_First_Circuit_Component" msprop:Generator_ColumnVarNameInTable="columnCable_To_First_Circuit_Component" type="xs:string" minOccurs="0" />
              <xs:element name="Cable_To_Remainder_Of_Circuit_Components" msprop:Generator_ColumnPropNameInTable="Cable_To_Remainder_Of_Circuit_ComponentsColumn" msprop:Generator_ColumnPropNameInRow="Cable_To_Remainder_Of_Circuit_Components" msprop:Generator_UserColumnName="Cable_To_Remainder_Of_Circuit_Components" msprop:Generator_ColumnVarNameInTable="columnCable_To_Remainder_Of_Circuit_Components" type="xs:string" minOccurs="0" />
              <xs:element name="Cable_Installation_Method" msprop:Generator_ColumnPropNameInTable="Cable_Installation_MethodColumn" msprop:Generator_ColumnPropNameInRow="Cable_Installation_Method" msprop:Generator_UserColumnName="Cable_Installation_Method" msprop:Generator_ColumnVarNameInTable="columnCable_Installation_Method" type="xs:string" minOccurs="0" />
              <xs:element name="Derating_Factor" msprop:Generator_ColumnPropNameInTable="Derating_FactorColumn" msprop:Generator_ColumnPropNameInRow="Derating_Factor" msprop:Generator_UserColumnName="Derating_Factor" msprop:Generator_ColumnVarNameInTable="columnDerating_Factor" type="xs:double" default="0" />
              <xs:element name="Diversity" msprop:Generator_ColumnPropNameInTable="DiversityColumn" msprop:Generator_ColumnPropNameInRow="Diversity" msprop:Generator_UserColumnName="Diversity" msprop:Generator_ColumnVarNameInTable="columnDiversity" type="xs:double" default="0" />
              <xs:element name="Path_Mode" msprop:Generator_ColumnPropNameInTable="Path_ModeColumn" msprop:Generator_ColumnPropNameInRow="Path_Mode" msprop:Generator_UserColumnName="Path_Mode" msprop:Generator_ColumnVarNameInTable="columnPath_Mode" type="xs:string" default="" minOccurs="0" />
              <xs:element name="Length_To_First" msprop:Generator_ColumnPropNameInTable="Length_To_FirstColumn" msprop:Generator_ColumnPropNameInRow="Length_To_First" msprop:Generator_UserColumnName="Length_To_First" msprop:Generator_ColumnVarNameInTable="columnLength_To_First" type="xs:double" default="0" />
              <xs:element name="Length_To_Final" msprop:Generator_ColumnPropNameInTable="Length_To_FinalColumn" msprop:Generator_ColumnPropNameInRow="Length_To_Final" msprop:Generator_UserColumnName="Length_To_Final" msprop:Generator_ColumnVarNameInTable="columnLength_To_Final" type="xs:double" default="0" />
              <xs:element name="PowerBimCurrent" msprop:Generator_ColumnPropNameInTable="PowerBimCurrentColumn" msprop:Generator_ColumnPropNameInRow="PowerBimCurrent" msprop:Generator_UserColumnName="PowerBimCurrent" msprop:Generator_ColumnVarNameInTable="columnPowerBimCurrent" type="xs:double" default="0" />
              <xs:element name="Number_Of_Elements" msprop:Generator_ColumnPropNameInTable="Number_Of_ElementsColumn" msprop:Generator_ColumnPropNameInRow="Number_Of_Elements" msprop:Generator_UserColumnName="Number_Of_Elements" msprop:Generator_ColumnVarNameInTable="columnNumber_Of_Elements" type="xs:double" default="0" />
              <xs:element name="Circuit_Description" msprop:Generator_ColumnPropNameInTable="Circuit_DescriptionColumn" msprop:Generator_ColumnPropNameInRow="Circuit_Description" msprop:Generator_UserColumnName="Circuit_Description" msprop:Generator_ColumnVarNameInTable="columnCircuit_Description" type="xs:string" minOccurs="0" />
              <xs:element name="Check_Trip_Rating" msprop:Generator_ColumnPropNameInTable="Check_Trip_RatingColumn" msprop:Generator_ColumnPropNameInRow="Check_Trip_Rating" msprop:Generator_UserColumnName="Check_Trip_Rating" msprop:Generator_ColumnVarNameInTable="columnCheck_Trip_Rating" type="xs:string" minOccurs="0" />
              <xs:element name="Cable_1_Valid" msprop:Generator_ColumnPropNameInTable="Cable_1_ValidColumn" msprop:Generator_ColumnPropNameInRow="Cable_1_Valid" msprop:Generator_UserColumnName="Cable_1_Valid" msprop:Generator_ColumnVarNameInTable="columnCable_1_Valid" type="xs:string" minOccurs="0" />
              <xs:element name="Cable_2_Valid" msprop:Generator_ColumnPropNameInTable="Cable_2_ValidColumn" msprop:Generator_ColumnPropNameInRow="Cable_2_Valid" msprop:Generator_UserColumnName="Cable_2_Valid" msprop:Generator_ColumnVarNameInTable="columnCable_2_Valid" type="xs:string" minOccurs="0" />
              <xs:element name="Check_CPD_Descriminates" msprop:Generator_ColumnPropNameInTable="Check_CPD_DescriminatesColumn" msprop:Generator_ColumnPropNameInRow="Check_CPD_Descriminates" msprop:Generator_UserColumnName="Check_CPD_Descriminates" msprop:Generator_ColumnVarNameInTable="columnCheck_CPD_Descriminates" type="xs:string" minOccurs="0" />
              <xs:element name="Check_Load_Current" msprop:Generator_ColumnPropNameInTable="Check_Load_CurrentColumn" msprop:Generator_ColumnPropNameInRow="Check_Load_Current" msprop:Generator_UserColumnName="Check_Load_Current" msprop:Generator_ColumnVarNameInTable="columnCheck_Load_Current" type="xs:string" minOccurs="0" />
              <xs:element name="Check_Cable_1_Current" msprop:Generator_ColumnPropNameInTable="Check_Cable_1_CurrentColumn" msprop:Generator_ColumnPropNameInRow="Check_Cable_1_Current" msprop:Generator_UserColumnName="Check_Cable_1_Current" msprop:Generator_ColumnVarNameInTable="columnCheck_Cable_1_Current" type="xs:string" minOccurs="0" />
              <xs:element name="Check_Cable_2_Current" msprop:Generator_ColumnPropNameInTable="Check_Cable_2_CurrentColumn" msprop:Generator_ColumnPropNameInRow="Check_Cable_2_Current" msprop:Generator_UserColumnName="Check_Cable_2_Current" msprop:Generator_ColumnVarNameInTable="columnCheck_Cable_2_Current" type="xs:string" minOccurs="0" />
              <xs:element name="Check_EFLI" msprop:Generator_ColumnPropNameInTable="Check_EFLIColumn" msprop:Generator_ColumnPropNameInRow="Check_EFLI" msprop:Generator_UserColumnName="Check_EFLI" msprop:Generator_ColumnVarNameInTable="columnCheck_EFLI" type="xs:string" minOccurs="0" />
              <xs:element name="Check_Final_CCT_VD" msprop:Generator_ColumnPropNameInTable="Check_Final_CCT_VDColumn" msprop:Generator_ColumnPropNameInRow="Check_Final_CCT_VD" msprop:Generator_UserColumnName="Check_Final_CCT_VD" msprop:Generator_ColumnVarNameInTable="columnCheck_Final_CCT_VD" type="xs:string" minOccurs="0" />
              <xs:element name="Check_System_Max_VD" msprop:Generator_ColumnPropNameInTable="Check_System_Max_VDColumn" msprop:Generator_ColumnPropNameInRow="Check_System_Max_VD" msprop:Generator_UserColumnName="Check_System_Max_VD" msprop:Generator_ColumnVarNameInTable="columnCheck_System_Max_VD" type="xs:string" minOccurs="0" />
              <xs:element name="Check_Cable_1_SC_Withstand" msprop:Generator_ColumnPropNameInTable="Check_Cable_1_SC_WithstandColumn" msprop:Generator_ColumnPropNameInRow="Check_Cable_1_SC_Withstand" msprop:Generator_UserColumnName="Check_Cable_1_SC_Withstand" msprop:Generator_ColumnVarNameInTable="columnCheck_Cable_1_SC_Withstand" type="xs:string" minOccurs="0" />
              <xs:element name="Check_Cable_2_SC_Withstand" msprop:Generator_ColumnPropNameInTable="Check_Cable_2_SC_WithstandColumn" msprop:Generator_ColumnPropNameInRow="Check_Cable_2_SC_Withstand" msprop:Generator_UserColumnName="Check_Cable_2_SC_Withstand" msprop:Generator_ColumnVarNameInTable="columnCheck_Cable_2_SC_Withstand" type="xs:string" minOccurs="0" />
              <xs:element name="Circuit_Check_Summary" msprop:Generator_ColumnPropNameInTable="Circuit_Check_SummaryColumn" msprop:Generator_ColumnPropNameInRow="Circuit_Check_Summary" msprop:Generator_UserColumnName="Circuit_Check_Summary" msprop:Generator_ColumnVarNameInTable="columnCircuit_Check_Summary" type="xs:string" minOccurs="0" />
              <xs:element name="Circuit_Check_Result" msprop:Generator_ColumnPropNameInTable="Circuit_Check_ResultColumn" msprop:Generator_ColumnPropNameInRow="Circuit_Check_Result" msprop:Generator_UserColumnName="Circuit_Check_Result" msprop:Generator_ColumnVarNameInTable="columnCircuit_Check_Result" type="xs:string" minOccurs="0" />
              <xs:element name="Circuit_Revision" msprop:Generator_ColumnPropNameInTable="Circuit_RevisionColumn" msprop:Generator_ColumnPropNameInRow="Circuit_Revision" msprop:Generator_UserColumnName="Circuit_Revision" msprop:Generator_ColumnVarNameInTable="columnCircuit_Revision" type="xs:string" minOccurs="0" />
              <xs:element name="isSpareOrSpace" msprop:Generator_ColumnPropNameInTable="isSpareOrSpaceColumn" msprop:Generator_ColumnPropNameInRow="isSpareOrSpace" msprop:Generator_UserColumnName="isSpareOrSpace" msprop:Generator_ColumnVarNameInTable="columnisSpareOrSpace" type="xs:boolean" minOccurs="0" />
              <xs:element name="Manual" msprop:Generator_ColumnPropNameInTable="ManualColumn" msprop:Generator_ColumnPropNameInRow="Manual" msprop:Generator_UserColumnName="Manual" msprop:Generator_ColumnVarNameInTable="columnManual" type="xs:string" minOccurs="0" />
              <xs:element name="ManualCurrent" msprop:Generator_ColumnPropNameInTable="ManualCurrentColumn" msprop:Generator_ColumnPropNameInRow="ManualCurrent" msprop:Generator_UserColumnName="ManualCurrent" msprop:Generator_ColumnVarNameInTable="columnManualCurrent" type="xs:boolean" minOccurs="0" />
              <xs:element name="ManualCurrentValue" msprop:Generator_UserColumnName="ManualCurrentValue" msprop:Generator_ColumnPropNameInTable="ManualCurrentValueColumn" msprop:Generator_ColumnPropNameInRow="ManualCurrentValue" msprop:Generator_ColumnVarNameInTable="columnManualCurrentValue" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>